# Core FastAPI and server dependencies
fastapi==0.116.1
uvicorn==0.35.0
python-multipart==0.0.20
python-dotenv==1.1.1
pydantic==2.11.7
starlette==0.47.2

# Database and AI dependencies
weaviate-client==4.16.3
openai==1.97.1

# Data processing and Excel handling
pandas==2.3.1
numpy==2.3.1
openpyxl==3.1.5
xlsxwriter==3.2.5

# HTTP client and networking
httpx==0.28.1
httpcore==1.0.9
certifi==2025.7.14

# Authentication and security
Authlib==1.6.1
cryptography==45.0.5

# Utility libraries
python-dateutil==2.9.0.post0
pytz==2025.2
tzdata==2025.2
six==1.17.0
tqdm==4.67.1
validators==0.35.0

# Type checking and annotations
typing-extensions==4.14.1
typing-inspection==0.4.1
annotated-types==0.7.0

# Async and networking support
anyio==4.9.0
sniffio==1.3.1
h11==0.16.0
click==8.2.1
idna==3.10

# gRPC and protocol buffers (for Weaviate)
grpcio==1.68.1
grpcio-health-checking==1.68.1
protobuf==5.29.0

# JSON processing
jiter==0.10.0

# System and packaging
distro==1.9.0
packaging==25.0
deprecation==2.1.0

# Low-level dependencies
cffi==1.17.1
pycparser==2.22
et_xmlfile==2.0.0

# Core Python dependencies
pydantic_core==2.33.2
setuptools==80.9.0
wheel==0.45.1
