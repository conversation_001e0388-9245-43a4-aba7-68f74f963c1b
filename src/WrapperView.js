import React, { useState, useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import { AuthenticatedTemplate, UnauthenticatedTemplate, useMsal } from '@azure/msal-react';
import { loginRequest } from './authconfig';
import Layout from './components/Layout';
import Home from './pages/Home';
import FileUpload from './pages/FileUpload';
import ManualEntry from './pages/ManualEntry';
import Preview from './pages/Preview';
import Export from './pages/Export';
import Logout from './pages/Logout';
import BigtappLogo from './assests/bigtapp-logo.png';

// Function to capture user information from Microsoft Graph API
const captureUserInfo = async (msalInstance, account) => {
  try {
    // Get access token for Microsoft Graph
    const graphRequest = {
      scopes: ["User.Read", "User.ReadBasic.All"],
      account: account
    };
   
    const response = await msalInstance.acquireTokenSilent(graphRequest);
   
    // Fetch user profile from Microsoft Graph API
    const graphResponse = await fetch('https://graph.microsoft.com/v1.0/me', {
      headers: {
        'Authorization': `Bearer ${response.accessToken}`,
        'Content-Type': 'application/json'
      }
    });
   
    if (!graphResponse.ok) {
      throw new Error(`Graph API request failed: ${graphResponse.status}`);
    }
   
    const userData = await graphResponse.json();
   
    // Extract user information
    const userInfo = {
      displayName: userData.displayName || account.name || account.username,
      givenName: userData.givenName || '',
      surname: userData.surname || '',
      email: userData.mail || account.username,
      avatar: null
    };
   
    // Try to get user photo
    try {
      const photoResponse = await fetch('https://graph.microsoft.com/v1.0/me/photo/$value', {
        headers: {
          'Authorization': `Bearer ${response.accessToken}`
        }
      });
     
      if (photoResponse.ok) {
        const photoBlob = await photoResponse.blob();
        const photoUrl = URL.createObjectURL(photoBlob);
        userInfo.avatar = photoUrl;
      }
    } catch (photoError) {
      console.log('User photo not available, using default avatar');
    }
   
    // Store user information in session storage
    sessionStorage.setItem('userInfo', JSON.stringify(userInfo));
    return userInfo;
  } catch (error) {
    console.error('Error fetching user information:', error);
    throw error;
  }
};

const WrapperView = () => {
  const [loginError, setLoginError] = useState(null);
  const [userInfo, setUserInfo] = useState(null);
  const { instance, accounts, inProgress } = useMsal();

  // Handle authentication state changes and user info loading
  useEffect(() => {
    const loadUserInfo = async () => {
      try {
        // First try to get from session storage
        const storedUserInfo = sessionStorage.getItem('userInfo');
        if (storedUserInfo) {
          setUserInfo(JSON.parse(storedUserInfo));
          return;
        }

        // If no stored info but we have an account, fetch from Graph API
        if (accounts.length > 0) {
          const activeAccount = accounts[0];
          if (!instance.getActiveAccount()) {
            instance.setActiveAccount(activeAccount);
          }
          const userInfo = await captureUserInfo(instance, activeAccount);
          setUserInfo(userInfo);
        }
      } catch (error) {
        console.error('Error loading user information:', error);
        // If there's an error fetching from Graph API, try to use basic account info
        if (accounts.length > 0) {
          const account = accounts[0];
          const basicInfo = {
            displayName: account.name || account.username,
            email: account.username,
            avatar: null
          };
          sessionStorage.setItem('userInfo', JSON.stringify(basicInfo));
          setUserInfo(basicInfo);
        }
      }
    };

    loadUserInfo();
  }, [instance, accounts, inProgress]);

  const handleLogin = async () => {
    try {
      setLoginError(null);
      
      // Try popup first for better user experience
      const response = await instance.loginPopup({
        ...loginRequest,
        prompt: 'select_account'
      });
      
      instance.setActiveAccount(response.account);
      
      // Capture user information after successful login
      try {
        const userInfo = await captureUserInfo(instance, response.account);
        setUserInfo(userInfo);
      } catch (error) {
        console.error('Error capturing user information after login:', error);
        // Use basic account info if Graph API fails
        const basicInfo = {
          displayName: response.account.name || response.account.username,
          email: response.account.username,
          avatar: null
        };
        sessionStorage.setItem('userInfo', JSON.stringify(basicInfo));
        setUserInfo(basicInfo);
      }
      
    } catch (error) {
      console.error('Login error:', error);
      setLoginError(error.message || 'Login failed. Please try again.');
      
      // Check for specific Azure AD errors
      if (error.errorCode === 'AADSTS9002326') {
        setLoginError('Azure AD configuration error: App must be configured as Single-Page Application (SPA). Please contact your administrator.');
      } else if (error.errorCode === 'AADSTS50020') {
        setLoginError('User account not found in the directory. Please contact your administrator.');
      } else if (error.errorCode === 'AADSTS50105') {
        setLoginError('The signed in user is not assigned to a role for the application. Please contact your administrator.');
      } else if (error.errorCode === 'AADSTS65001') {
        setLoginError('The user or administrator has not consented to use the application. Please contact your administrator.');
      }
      
      // Fallback to redirect if popup fails
      try {
        await instance.loginRedirect({
          ...loginRequest,
          prompt: 'select_account'
        });
      } catch (redirectError) {
        console.error('Login redirect failed:', redirectError);
        setLoginError('Both popup and redirect login methods failed. Please try refreshing the page.');
      }
    }
  };

  // Pass userInfo to Layout component
  const renderLayout = (children) => (
    <Layout userInfo={userInfo}>
      {children}
    </Layout>
  );

  return (
    <div className="App">
      <Routes>
        {/* Logout route should be accessible regardless of auth state */}
        <Route path="/logout" element={<Logout />} />
        <Route path="/*" element={
          <>
            <AuthenticatedTemplate>
              {renderLayout(
                <Routes>
                  <Route path="/" element={<Preview />} />
                  <Route path="/home" element={<Home />} />
                  <Route path="/file-upload" element={<FileUpload />} />
                  <Route path="/manual-entry" element={<ManualEntry />} />
                  <Route path="/preview" element={<Preview />} />
                  <Route path="/export" element={<Export />} />
                </Routes>
              )}
            </AuthenticatedTemplate>

            <UnauthenticatedTemplate>
              <div className="login-container">
                <div className="login-content">
                  <div className="login-box">
                    <div className="text-center">
                      <img src={BigtappLogo} alt="Bigtapp Logo" className="login-logo" />
                      <h2 className="login-title">
                        Welcome to UseCase Buddy
                      </h2>
                      <p className="login-subtitle">
                        Please sign in to continue
                      </p>
                    </div>

                    {loginError && (
                      <div className="alert alert-error">
                        <div className="alert-content">
                          <div className="alert-icon">
                            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <p className="alert-message">{loginError}</p>
                        </div>
                      </div>
                    )}

                    <button
                      onClick={handleLogin}
                      disabled={inProgress !== 'none'}
                      className="btn-primary login-button"
                    >
                      {inProgress !== 'none' ? 'Signing in...' : 'Sign in with Microsoft'}
                    </button>

                    {inProgress !== 'none' && (
                      <div className="login-progress">
                        <p>Authentication in progress... Please wait.</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </UnauthenticatedTemplate>
          </>
        } />
      </Routes>
    </div>
  );
};

export default WrapperView; 