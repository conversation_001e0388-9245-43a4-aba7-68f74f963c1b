import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { FiHome, FiUpload, FiEdit3, FiEye, FiDownload, FiMenu, FiLogOut } from 'react-icons/fi';
import { useMsal } from '@azure/msal-react';


const Layout = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [userInfo, setUserInfo] = useState(null);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { accounts } = useMsal();

  useEffect(() => {
    const loadUserInfo = () => {
      // First try to get from session storage
      const storedUserInfo = sessionStorage.getItem('userInfo');
      if (storedUserInfo) {
        try {
          const parsedUserInfo = JSON.parse(storedUserInfo);
          setUserInfo(parsedUserInfo);
          return;
        } catch (error) {
          console.error('Error parsing stored user info:', error);
        }
      }

      // If no stored info but we have an account, use basic account info
      if (accounts.length > 0) {
        const account = accounts[0];
        const basicInfo = {
          displayName: account.name || account.username,
          email: account.username,
          avatar: null
        };
        sessionStorage.setItem('userInfo', JSON.stringify(basicInfo));
        setUserInfo(basicInfo);
      }
    };

    loadUserInfo();

    // Set up event listener for storage changes
    const handleStorageChange = (e) => {
      if (e.key === 'userInfo') {
        loadUserInfo();
      }
    };
    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [accounts]);

  const handleLogout = () => {
    // Navigate to logout page first, let it handle the cleanup
    navigate('/logout');
  };

  const navItems = [
    { path: '/home', icon: FiHome, label: 'Home' },
    { path: '/preview', icon: FiEye, label: 'Usecase Repository' },
    { path: '/file-upload', icon: FiUpload, label: 'File Upload' },
    { path: '/manual-entry', icon: FiEdit3, label: 'Manual Entry' },
    { path: '/export', icon: FiDownload, label: 'Export' }
  ];



  return (
    <div className="layout-container">
      {/* Sidebar */}
      <div className={`sidebar ${!sidebarOpen ? 'collapsed' : ''}`}>
        <div className="sidebar-header">
          <Link to="/" className="logo">
            <div className="logo-icon" style={{
              background: 'transparent',
              padding: '0px',
              width: '40px',
              height: '40px',
              overflow: 'hidden',
              borderRadius: '6px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <img
                src="/usecase-buddy-logo.png"
                alt="Usecase Buddy"
                style={{
                  width: '60px',
                  height: '60px',
                  objectFit: 'cover',
                  objectPosition: 'center'
                }}
              />
            </div>
            <div>
              <div className="logo-text">UseCase Buddy</div>
              <div style={{ fontSize: '12px', opacity: 0.8 }}></div>
            </div>
          </Link>
        </div>
        
        <nav className="sidebar-nav">
          {navItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path ||
              (item.path === '/preview' && location.pathname === '/');
            
            return (
              <Link
                key={item.path}
                to={item.path}
                className={`nav-item ${isActive ? 'active' : ''}`}
                onClick={() => setSidebarOpen(false)}
              >
                <Icon className="nav-item-icon" />
                {item.label}
              </Link>
            );
          })}
        </nav>
      </div>

      {/* Main Content */}
      <div className={`main-content ${!sidebarOpen ? 'sidebar-collapsed' : ''}`}>
        {/* Header */}
        <header className="header">
          <div className="header-left">
            <button
              className="hamburger-menu-btn"
              onClick={() => setSidebarOpen(!sidebarOpen)}
              style={{
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                fontSize: '20px',
                color: '#64748b',
                marginRight: '16px',
                padding: '8px',
                borderRadius: '4px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                transition: 'background-color 0.2s ease'
              }}
              onMouseEnter={(e) => e.target.style.backgroundColor = '#f1f5f9'}
              onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
            >
              <FiMenu />
            </button>
            <img
              src="/beeonix-logo.png"
              alt="Beonix AI"
              style={{
                height: '48px',
                width: 'auto',
                objectFit: 'contain'
              }}
              onError={(e) => {
                // Fallback to text if image doesn't load
                e.target.style.display = 'none';
                e.target.nextSibling.style.display = 'block';
              }}
            />
            <div style={{ display: 'none', fontSize: '14px', fontWeight: '500', color: '#64748b' }}>
              Beonix AI
            </div>
          </div>
          
          <div className="header-center">
            <div className="header-title">UseCase Buddy</div>
          </div>
          
          <div className="user-info">
            <img
              src="/bigtapp-logo.png"
              alt="BigTapp"
              style={{
                height: '52px',
                width: 'auto',
                objectFit: 'contain',
                marginRight: '16px'
              }}
              onError={(e) => {
                // Fallback to text if image doesn't load
                e.target.style.display = 'none';
                e.target.nextSibling.style.display = 'inline-block';
              }}
            />
            <div style={{ display: 'none', marginRight: '16px', fontSize: '14px', fontWeight: '500', color: '#64748b' }}>
              BigTapp
            </div>
            {userInfo && (
              <div className="user-menu-container">
                <button 
                  className="user-menu-trigger"
                  onClick={() => setShowUserMenu(!showUserMenu)}
                >
                  <div className="user-details">
                    <div className="user-name">{userInfo.displayName}</div>
                    <div className="user-email">{userInfo.email}</div>
                  </div>
                  {userInfo.avatar ? (
                    <img
                      src={userInfo.avatar}
                      alt={userInfo.displayName}
                      className="user-avatar-img"
                    />
                  ) : (
                    <div className="user-avatar">
                      {userInfo.displayName?.charAt(0).toUpperCase() || 'U'}
                    </div>
                  )}
                </button>
                
                {showUserMenu && (
                  <div className="user-menu-dropdown">
                    <div className="user-menu-header">
                      <div className="user-menu-info">
                        <div className="user-menu-details">
                          <div className="user-menu-name">{userInfo.displayName}</div>
                          <div className="user-menu-email">{userInfo.email}</div>
                        </div>
                        {userInfo.avatar ? (
                          <img 
                            src={userInfo.avatar} 
                            alt={userInfo.displayName} 
                            className="user-menu-avatar-img"
                          />
                        ) : (
                          <div className="user-menu-avatar">
                            {userInfo.displayName?.charAt(0).toUpperCase() || 'U'}
                          </div>
                        )}
                      </div>
                    </div>
                    <button className="user-menu-item" onClick={handleLogout}>
                      <FiLogOut className="menu-item-icon" />
                      <span>Logout</span>
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </header>

        {/* Page Content */}
        <main className="page-content">
          {children}
        </main>
      </div>

      {/* Mobile overlay */}
      {sidebarOpen && (
        <div
          className="mobile-overlay"
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0, 0, 0, 0.5)',
            zIndex: 999,
            display: 'none'
          }}
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};

export default Layout; 