import os
import io
import uuid
import pandas as pd
from fastapi import FastAPI, UploadFile, File, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from starlette.responses import Response
from pydantic import BaseModel
from typing import List, Optional, Any
from dotenv import load_dotenv
import weaviate
import weaviate.classes as wvc
from weaviate.classes.query import MetadataQuery
from openai import AzureOpenAI
import numpy as np
import hashlib
import json
from functools import lru_cache
import concurrent.futures
import re
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    try:
        get_openai_client()
        client = get_weaviate_client()
        if client:
            setup_weaviate_schema(client)
        print("✅ Application startup completed")
    except Exception as e:
        print(f"⚠️ Warning during startup: {e}")
        print("Application will continue but some features may not work")

    yield

    # Shutdown
    global weaviate_client
    if weaviate_client:
        try:
            weaviate_client.close()
        except:
            pass

app = FastAPI(lifespan=lifespan)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5001"],  # React frontend URL
    allow_credentials=True,
    allow_methods=["*"],  # Allow all HTTP methods
    allow_headers=["*"],  # Allow all headers
)

# Load environment variables
load_dotenv()

WEAVIATE_API_KEY = os.getenv("WEAVIATE_API_KEY")
WEAVIATE_URL = os.getenv("WEAVIATE_URL")
AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY")
AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
AZURE_OPENAI_API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION")
AZURE_OPENAI_DEPLOYMENT_NAME = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME")
AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME = os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME")

openai_client = None
weaviate_client = None

def get_openai_client():
    global openai_client
    if openai_client is not None:
        return openai_client
    client = AzureOpenAI(
        api_key=AZURE_OPENAI_API_KEY,
        api_version=AZURE_OPENAI_API_VERSION,
        azure_endpoint=AZURE_OPENAI_ENDPOINT
    )
    openai_client = client
    return client

def get_weaviate_client():
    global weaviate_client
    if weaviate_client is not None:
        return weaviate_client

    # Check if required environment variables are set
    if not WEAVIATE_API_KEY or not WEAVIATE_URL:
        print("⚠️ Weaviate credentials not configured. Some features will be unavailable.")
        return None

    try:
        if AZURE_OPENAI_API_VERSION:
            os.environ["OPENAI_API_VERSION"] = AZURE_OPENAI_API_VERSION
        headers = {"X-Azure-Api-Key": AZURE_OPENAI_API_KEY}
        client = weaviate.connect_to_weaviate_cloud(
            cluster_url=WEAVIATE_URL,
            auth_credentials=weaviate.auth.AuthApiKey(WEAVIATE_API_KEY),
            headers=headers,
            skip_init_checks=True
        )
        weaviate_client = client
        return client
    except Exception as e:
        print(f"⚠️ Failed to connect to Weaviate: {e}")
        return None

def setup_weaviate_schema(client):
    if not client:
        print("⚠️ No Weaviate client available, skipping schema setup")
        return

    try:
        collections = client.collections.list_all()
        collection_names = [c if isinstance(c, str) else getattr(c, 'name', None) for c in collections]
        if "UseCase" not in collection_names:
            azure_resource_name = None
            if AZURE_OPENAI_ENDPOINT:
                if '//' in AZURE_OPENAI_ENDPOINT:
                    azure_resource_name = AZURE_OPENAI_ENDPOINT.split('.')[0].split('//')[1]
                else:
                    azure_resource_name = AZURE_OPENAI_ENDPOINT.split('.')[0]
            client.collections.create(
            name="UseCase",
            description="A collection for storing use cases and their attributes",
            vectorizer_config=wvc.config.Configure.Vectorizer.text2vec_azure_openai(
                vectorize_collection_name=False,
                base_url=AZURE_OPENAI_ENDPOINT,
                resource_name=azure_resource_name,
                deployment_id=AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME,
            ),
            properties=[
                wvc.config.Property(name="use_case_no", data_type=wvc.config.DataType.TEXT, description="The use case number"),
                wvc.config.Property(name="use_case_text", data_type=wvc.config.DataType.TEXT, description="The detailed description of the use case", skip_vectorization=False, vectorize_property_name=False),
                wvc.config.Property(name="user_id", data_type=wvc.config.DataType.TEXT, description="User ID of the person who created this use case"),
                wvc.config.Property(name="applicable_banking_domains", data_type=wvc.config.DataType.TEXT, description="Banking domains where this use case applies"),
                wvc.config.Property(name="business_value", data_type=wvc.config.DataType.TEXT, description="The business value of the use case"),
                wvc.config.Property(name="focus_area", data_type=wvc.config.DataType.TEXT, description="The focus area of the use case"),
                wvc.config.Property(name="topline_impact", data_type=wvc.config.DataType.BOOL, description="Whether the use case has topline impact"),
                wvc.config.Property(name="bottomline_impact", data_type=wvc.config.DataType.BOOL, description="Whether the use case has bottomline impact"),
                wvc.config.Property(name="regulatory_impact", data_type=wvc.config.DataType.BOOL, description="Whether the use case has regulatory impact"),
                wvc.config.Property(name="technical_feasibility", data_type=wvc.config.DataType.NUMBER, description="Technical feasibility rating (1-5, 1 is high)"),
                wvc.config.Property(name="operational_feasibility", data_type=wvc.config.DataType.NUMBER, description="Operational feasibility rating (1-5, 1 is high)"),
                wvc.config.Property(name="business_feasibility", data_type=wvc.config.DataType.NUMBER, description="Business feasibility rating (1-5, 1 is high)"),
                wvc.config.Property(name="ai_techniques_used", data_type=wvc.config.DataType.TEXT, description="AI techniques used in the use case"),
                wvc.config.Property(name="savings_benefit_percentage", data_type=wvc.config.DataType.NUMBER, description="Approximate percentage of savings benefit"),
                wvc.config.Property(name="status", data_type=wvc.config.DataType.TEXT, description="Current status of the use case")
            ]
        )
    except Exception as e:
        print(f"⚠️ Failed to setup Weaviate schema: {e}")

@app.get("/api/health")
def health_check():
    """Health check endpoint for Docker health checks"""
    status = {"status": "healthy", "message": "Service is running"}

    # Check external service connections
    services = {}

    # Check Weaviate connection
    try:
        client = get_weaviate_client()
        if client:
            services["weaviate"] = "connected"
        else:
            services["weaviate"] = "disconnected"
    except:
        services["weaviate"] = "error"

    # Check OpenAI connection
    try:
        openai_client = get_openai_client()
        if openai_client:
            services["openai"] = "connected"
        else:
            services["openai"] = "disconnected"
    except:
        services["openai"] = "error"

    status["services"] = services
    return status

# --- Models ---
class UseCaseAttributes(BaseModel):
    applicable_banking_domains: str
    business_value: str
    focus_area: str
    topline_impact: bool
    bottomline_impact: bool
    regulatory_impact: bool
    technical_feasibility: int
    operational_feasibility: int
    business_feasibility: int
    ai_techniques_used: str
    savings_benefit_percentage: float
    status: Optional[str] = ""

class UseCaseRequest(BaseModel):
    use_case_no: Optional[str] = None  # Make optional
    use_case_text: str
    user_id: Optional[str] = None
    attributes: Optional[UseCaseAttributes] = None

class DuplicateCheckRequest(BaseModel):
    use_case_text: str
    alpha: Optional[float] = 0.5
    limit: Optional[int] = 5
    confirmed_threshold: Optional[float] = 0.85
    potential_threshold: Optional[float] = 0.60
    search_strategy: Optional[str] = "standard"  # "standard" or "enhanced"

class DuplicateCategoryResult(BaseModel):
    use_case_no: str
    use_case_text: str
    score: float
    word_overlap: float
    category: str  # "confirmed_duplicate", "potential_duplicate", "new_use_case"

class ProcessFileResult(BaseModel):
    results: List[dict]  # Confirmed duplicates
    potential: List[dict]  # Potential duplicates
    added: int  # Number of new use cases added

class SingleUseCaseRequest(BaseModel):
    use_case_no: Optional[str] = None  # Make optional
    use_case_text: str
    user_id: Optional[str] = None
    alpha: Optional[float] = 0.5
    confirmed_threshold: Optional[float] = 0.85
    potential_threshold: Optional[float] = 0.60

class SingleUseCaseResult(BaseModel):
    use_case_no: Optional[str] = None  # Make optional
    use_case_text: str
    status: str  # "confirmed_duplicate", "potential_duplicate", "added", "skipped", "error"
    duplicate_info: Optional[dict] = None
    uuid: Optional[str] = None
    error: Optional[str] = None

# Add this model class
class UpdateStatusRequest(BaseModel):
    uuid: str
    status: str

class MultipleStatusUpdatesRequest(BaseModel):
    updates: List[UpdateStatusRequest]

class UpdateUseCaseRequest(BaseModel):
    uuid: str
    use_case_text: Optional[str] = None
    applicable_banking_domains: Optional[str] = None
    business_value: Optional[str] = None
    focus_area: Optional[str] = None
    topline_impact: Optional[bool] = None
    bottomline_impact: Optional[bool] = None
    regulatory_impact: Optional[bool] = None
    technical_feasibility: Optional[int] = None
    operational_feasibility: Optional[int] = None
    business_feasibility: Optional[int] = None
    ai_techniques_used: Optional[str] = None
    savings_benefit_percentage: Optional[float] = None
    status: Optional[str] = None

# --- Helper Functions ---
def parse_attributes(result):
    attributes = {}
    expected_keys = [
        "applicable_banking_domains",
        "business_value",
        "focus_area",
        "topline_impact",
        "bottomline_impact",
        "regulatory_impact",
        "technical_feasibility",
        "operational_feasibility",
        "business_feasibility",
        "ai_techniques_used",
        "savings_benefit_percentage",
        "status"
    ]
    for line in result.strip().split('\n'):
        if ':' in line:
            key, value = line.split(':', 1)
            key = key.strip().lower().replace(' ', '_').replace('.', '').replace('(', '').replace(')', '')
            value = value.strip()
            if value.lower() == 'yes':
                value = True
            elif value.lower() == 'no':
                value = False
            if key in ['technical_feasibility', 'operational_feasibility', 'business_feasibility']:
                try:
                    first_word = value.split()[0]
                    if first_word.isdigit():
                        value = int(first_word)
                    else:
                        value = 3
                except Exception:
                    value = 3
                if isinstance(value, int) and (value < 1 or value > 5):
                    value = max(1, min(5, value))
            if key == 'savings_benefit_percentage':
                try:
                    numeric_value = value.replace('%', '').strip()
                    value = float(numeric_value)
                except Exception:
                    value = 0
            attributes[key] = value
    for key in expected_keys:
        if key not in attributes:
            if key in ['topline_impact', 'bottomline_impact', 'regulatory_impact']:
                attributes[key] = False
            elif key in ['technical_feasibility', 'operational_feasibility', 'business_feasibility']:
                attributes[key] = 3
            elif key == 'savings_benefit_percentage':
                attributes[key] = 0
            elif key == 'status':
                attributes[key] = ""
            else:
                attributes[key] = "Not specified"
    return attributes

def get_default_attributes():
    return {
        "applicable_banking_domains": "Error in processing",
        "business_value": "Error in processing",
        "focus_area": "Error in processing",
        "topline_impact": False,
        "bottomline_impact": False,
        "regulatory_impact": False,
        "technical_feasibility": 3,
        "operational_feasibility": 3,
        "business_feasibility": 3,
        "ai_techniques_used": "Error in processing",
        "savings_benefit_percentage": 0,
        "status": "not set"
    }

def generate_attributes(use_case_text: str):
    client = get_openai_client()
    prompt = f"""
    Analyze the following AI use case and provide the attributes as requested.
    Your response should be in a clear format with labels and values that can be easily parsed.
    Use Case: {use_case_text}
    Please provide ONLY the following information in a structured format with each attribute on a new line:
    1. Applicable Banking Domains: [list the relevant banking domains, separated by commas]
    2. Business Value: [provide a concise description of the business value]
    3. Focus Area: [identify the main focus area]
    4. Topline Impact: [answer ONLY with \"yes\" or \"no\"]
    5. Bottomline Impact: [answer ONLY with \"yes\" or \"no\"]
    6. Regulatory Impact: [answer ONLY with \"yes\" or \"no\"]
    7. Technical Feasibility: [provide ONLY a number from 1 to 5, where 1 is high and 5 is low]
    8. Operational Feasibility: [provide ONLY a number from 1 to 5, where 1 is high and 5 is low]
    9. Business Feasibility: [provide ONLY a number from 1 to 5, where 1 is high and 5 is low]
    10. AI Techniques Used: [list the AI techniques, separated by commas]
    11. Savings Benefit Percentage: [provide ONLY a number representing the approximate percentage]
    12. Status: [provide the current status of the use case - always use \"not set\" for new use cases]
    Format your response exactly like this example:
    Applicable Banking Domains: Retail Banking, Corporate Banking
    Business Value: Reduces operational costs and improves customer satisfaction
    Focus Area: Customer Onboarding
    Topline Impact: yes
    Bottomline Impact: yes
    Regulatory Impact: no
    Technical Feasibility: 2
    Operational Feasibility: 3
    Business Feasibility: 1
    AI Techniques Used: Natural Language Processing, Computer Vision, Machine Learning
    Savings Benefit Percentage: 15
    Status: not set
    """
    try:
        response = client.chat.completions.create(
            model=AZURE_OPENAI_DEPLOYMENT_NAME,
            messages=[
                {"role": "system", "content": "You are an AI expert who analyzes banking and financial use cases and extracts structured information with precision."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1,
            max_tokens=2000
        )
        result = response.choices[0].message.content
        return parse_attributes(result)
    except Exception:
        return get_default_attributes()

# Simple in-memory cache for attribute generation
_attribute_cache = {}

def get_cache_key(use_case_text: str) -> str:
    """Generate a cache key for use case text"""
    return hashlib.md5(use_case_text.lower().strip().encode()).hexdigest()

@lru_cache(maxsize=100)
def generate_attributes_cached(use_case_text: str):
    """
    Cached version of generate_attributes to avoid duplicate API calls
    """
    cache_key = get_cache_key(use_case_text)

    # Check cache first
    if cache_key in _attribute_cache:
        print(f"Cache hit for use case: {use_case_text[:50]}...")
        return _attribute_cache[cache_key]

    # Generate attributes if not in cache
    attributes = generate_attributes(use_case_text)

    # Store in cache
    _attribute_cache[cache_key] = attributes
    print(f"Cache miss - generated attributes for: {use_case_text[:50]}...")

    return attributes

def process_new_use_cases_batch(new_use_cases):
    """
    Optimized batch processing for new use cases.
    Features:
    - Parallel processing with threading
    - Caching to avoid duplicate API calls
    - Batch size optimization for rate limits
    """
    added_count = 0
    batch_size = 3  # Process 3 use cases concurrently to avoid rate limits

    def process_single_use_case(use_case_data):
        use_case_no, use_case_text = use_case_data
        try:
            # Use cached attribute generation
            attributes = generate_attributes_cached(use_case_text)
            attributes["status"] = "not set"

            # Add to database (use_case_no will be auto-generated if None)
            success, _ = add_use_case(use_case_no, use_case_text, attributes)
            return 1 if success else 0
        except Exception as e:
            print(f"Error processing use case {use_case_no or 'AUTO'}: {e}")
            return 0

    # Process use cases in batches with threading for better performance
    for i in range(0, len(new_use_cases), batch_size):
        batch = new_use_cases[i:i + batch_size]

        with concurrent.futures.ThreadPoolExecutor(max_workers=batch_size) as executor:
            # Submit all tasks in the batch
            futures = [executor.submit(process_single_use_case, use_case) for use_case in batch]

            # Collect results as they complete
            for future in concurrent.futures.as_completed(futures):
                try:
                    added_count += future.result()
                except Exception as e:
                    print(f"Error in batch processing: {e}")

    return added_count

def is_valid_comparison_text(text1, text2):
    """
    Check if the duplicate text is valid for comparison.
    Filters out placeholder texts and ensures minimum content.
    
    Args:
        text1: First text to compare
        text2: Second text to compare
        
    Returns:
        bool: True if valid for comparison, False otherwise
    """
    # Ensure both texts exist
    if not text1 or not text2:
        return False
    
    # Convert to strings and clean
    text1 = str(text1).strip().lower()
    
    # List of placeholder or meaningless texts to filter out
    placeholder_texts = [
        "mirror", "duplicate", "same", "copy", "n/a", "na", "not applicable", 
        "tbd", "to be determined", "pending", "placeholder", "test"
    ]
    
    # Check if text1 is just a placeholder or too short
    if text1 in placeholder_texts or len(text1) < 10 or len(text1.split()) < 2:
        return False
    
    return True

def assess_similarity_with_llm(openai_client, use_case_text, potential_duplicate_text):
    """
    Use LLM to assess the similarity between two use cases and provide an explanation.
    
    Args:
        openai_client: The Azure OpenAI client
        use_case_text: The original use case text
        potential_duplicate_text: The potential duplicate use case text
        
    Returns:
        dict: Contains similarity score (0-1) and explanation
    """
    if openai_client is None:
        return {"score": 0.0, "explanation": "Azure OpenAI client is not initialized"}
    
    # Perform basic validation
    if not is_valid_comparison_text(potential_duplicate_text, use_case_text):
        return {"score": 0.0, "explanation": "Invalid comparison - one text is too short or a placeholder"}
    
    # First check for exact or near-exact matches
    use_case_clean = use_case_text.strip().lower()
    potential_duplicate_clean = potential_duplicate_text.strip().lower()
    
    # If texts are identical, return highest score immediately
    if use_case_clean == potential_duplicate_clean:
        return {"score": 1.0, "explanation": "The use cases are identical."}
    
    # If one text fully contains the other, it's likely a very close match
    if (len(use_case_clean) > 20 and len(potential_duplicate_clean) > 20 and 
        (use_case_clean in potential_duplicate_clean or potential_duplicate_clean in use_case_clean)):
        return {"score": 0.95, "explanation": "One use case fully contains the other - they are essentially the same."}
        
    prompt = f"""
    Compare these two AI use cases and determine if they represent the SAME SPECIFIC business solution or just similar goals.
    
    EXISTING USE CASE: {potential_duplicate_text}
    
    YOUR USE CASE: {use_case_text}
    
    STRICT EVALUATION CRITERIA - A use case is a duplicate ONLY if it has:
    1. IDENTICAL data sources (same input data types)
    2. IDENTICAL AI techniques/methods (same technical approach)  
    3. IDENTICAL specific business process being automated
    4. IDENTICAL target users and use context
    
    SCORING GUIDELINES:
    - 0.9-1.0: Nearly identical use cases (same data, same AI method, same process)
    - 0.8-0.9: Very similar use cases with minor differences in implementation
    - 0.6-0.8: Related use cases but different data sources OR different AI techniques OR different processes
    - 0.4-0.6: Same business domain but different specific solutions
    - 0.0-0.4: Different business problems or completely different approaches
    
    Be CONSERVATIVE with high scores. Similar business outcomes do NOT make use cases duplicates if they use different data sources or AI techniques.
    
    You MUST respond in this EXACT format:
    
    Similarity Score: 0.65
    Explanation: Brief explanation focusing on specific differences in data sources, AI techniques, or processes
    """
    
    try:
        response = openai_client.chat.completions.create(
            model=AZURE_OPENAI_DEPLOYMENT_NAME,
            messages=[
                {"role": "system", "content": "You are an AI expert who specializes in understanding banking and financial use cases. You can identify when two differently worded descriptions are referring to the same underlying business need or solution."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1,
            max_tokens=500
        )
        
        result = response.choices[0].message.content
        
        # Parse the response to extract score and explanation
        score = 0.0
        explanation = "No explanation provided"
        
        # More robust parsing - handle different response formats
        lines = result.strip().split('\n')
        for line in lines:
            line_lower = line.lower().strip()
            
            # Look for similarity score in various formats
            if any(phrase in line_lower for phrase in ["similarity score:", "score:", "similarity:"]):
                try:
                    # Extract everything after the colon
                    if ':' in line:
                        score_text = line.split(':', 1)[1].strip()
                        # Remove common text that might follow the score
                        score_text = score_text.split()[0]  # Take only the first word/number
                        # Handle various formats (0.8, 80%, 8/10, etc.)
                        score_text = score_text.replace('%', '').replace('/10', '').replace('/100', '')
                        score = float(score_text)
                        # Normalize to 0-1 range
                        if score > 1:
                            score = score / 100
                        break
                except Exception as e:
                    continue
        
        # Extract explanation - look for explanation line or use the whole response if score is found
        explanation_found = False
        for line in lines:
            line_lower = line.lower().strip()
            if "explanation:" in line_lower:
                explanation = line.split(':', 1)[1].strip()
                explanation_found = True
                break
        
        # If no explicit explanation found, use the response after the score line
        if not explanation_found and score > 0:
            explanation_lines = []
            score_line_found = False
            for line in lines:
                if score_line_found and line.strip():
                    explanation_lines.append(line.strip())
                elif any(phrase in line.lower() for phrase in ["similarity score:", "score:", "similarity:"]):
                    score_line_found = True
            if explanation_lines:
                explanation = " ".join(explanation_lines)
        
        # Safety check - if we still have score 0 and LLM responded, use a minimal score
        if score == 0.0 and result.strip() and len(result.strip()) > 10:
            score = 0.1  # Minimal score to indicate LLM processed but found low similarity
            if not explanation or explanation == "No explanation provided":
                explanation = f"LLM analysis available but score extraction failed. Response: {result[:100]}..."
        
        return {"score": score, "explanation": explanation}
        
    except Exception as e:
        return {"score": 0.0, "explanation": f"Error in similarity assessment: {str(e)}"}

def merge_search_results(keyword_response, vector_response):
    """
    Merge and deduplicate results from keyword and vector searches.
    Prioritizes keyword matches and combines scores intelligently.
    """
    seen_uuids = set()
    merged_results = []

    # First, add all keyword search results (higher priority)
    if keyword_response and keyword_response.objects:
        for obj in keyword_response.objects:
            uuid_str = str(obj.uuid) if hasattr(obj, 'uuid') else ""
            if uuid_str not in seen_uuids:
                seen_uuids.add(uuid_str)
                # Mark as keyword match for scoring boost
                obj._search_type = "keyword"
                merged_results.append(obj)

    # Then, add vector search results that weren't already found
    if vector_response and vector_response.objects:
        for obj in vector_response.objects:
            uuid_str = str(obj.uuid) if hasattr(obj, 'uuid') else ""
            if uuid_str not in seen_uuids:
                seen_uuids.add(uuid_str)
                obj._search_type = "vector"
                merged_results.append(obj)

    return merged_results

def check_for_duplicates_enhanced(use_case_text: str, alpha=0.5, limit=5, confirmed_threshold=0.85, potential_threshold=0.60):
    """
    Enhanced duplicate detection using dual search strategy (keyword + vector).
    Specifically designed for manual entry to catch keyword matches like 'Copilot'.
    """
    client = get_weaviate_client()
    openai_client = get_openai_client()

    if not client or not use_case_text:
        return {"category": "new_use_case", "confirmed": None, "potential": [], "matches": []}

    try:
        # Get the collection
        collection = client.collections.get("UseCase")

        # First, check for exact text matches before doing hybrid search
        all_objects_response = collection.query.fetch_objects(limit=1000)
        exact_match = None

        for obj in all_objects_response.objects:
            stored_text = obj.properties.get("use_case_text", "").strip()
            if stored_text.lower() == use_case_text.strip().lower():
                exact_match = obj
                break

        # If we found an exact match, return it as confirmed duplicate
        if exact_match:
            exact_result = {
                "use_case_no": exact_match.properties.get("use_case_no", ""),
                "use_case_text": exact_match.properties.get("use_case_text", "").strip(),
                "vector_score": 1.0,  # Perfect match
                "uuid": str(exact_match.uuid) if hasattr(exact_match, 'uuid') else "",
                "score": 1.0,
                "explanation": "The use cases are identical.",
                "word_overlap": 1.0
            }
            return {"category": "confirmed_duplicate", "confirmed": exact_result, "potential": [], "matches": [exact_result]}

        # Step 1: Keyword-only search (alpha=0.0 = 100% keyword)
        keyword_response = collection.query.hybrid(
            query=use_case_text,
            limit=limit,
            alpha=0.0,  # Pure keyword search
            return_metadata=MetadataQuery(score=True)
        )

        # Step 2: Vector-heavy search (alpha=0.8 = 80% vector, 20% keyword)
        vector_response = collection.query.hybrid(
            query=use_case_text,
            limit=limit,
            alpha=0.8,  # Vector-heavy search
            return_metadata=MetadataQuery(score=True)
        )

        # Step 3: Merge and deduplicate results
        merged_objects = merge_search_results(keyword_response, vector_response)

        if not merged_objects:
            return {"category": "new_use_case", "confirmed": None, "potential": [], "matches": []}

        # Format results with enhanced scoring
        formatted_results = []
        for obj in merged_objects:
            duplicate_text = obj.properties.get("use_case_text", "").strip()
            if not is_valid_comparison_text(duplicate_text, use_case_text):
                continue

            vector_score = 0
            if obj.metadata and obj.metadata.score is not None:
                vector_score = obj.metadata.score

            result_item = {
                "use_case_no": obj.properties.get("use_case_no", ""),
                "use_case_text": duplicate_text,
                "vector_score": vector_score,
                "uuid": str(obj.uuid) if hasattr(obj, 'uuid') else ""
            }

            # Always use LLM for similarity assessment if available
            if openai_client:
                llm_assessment = assess_similarity_with_llm(
                    openai_client,
                    use_case_text,
                    duplicate_text
                )

                # Enhanced scoring: boost keyword matches
                final_score = llm_assessment["score"]
                if hasattr(obj, '_search_type') and obj._search_type == "keyword":
                    # Boost keyword matches by 0.1 (but cap at 1.0)
                    final_score = min(1.0, final_score + 0.1)
                    explanation = f"[Keyword Match] {llm_assessment['explanation']}"
                else:
                    explanation = llm_assessment["explanation"]

                result_item.update({
                    "score": final_score,
                    "explanation": explanation,
                    "word_overlap": 0  # Keep for compatibility
                })
            else:
                # Fallback to vector score if LLM is not available
                result_item.update({
                    "score": vector_score,
                    "explanation": "Similarity based on search only (LLM not available).",
                    "word_overlap": 0  # Keep for compatibility
                })

            formatted_results.append(result_item)

        # Sort results by similarity score
        formatted_results.sort(key=lambda x: x["score"], reverse=True)

        top_match = formatted_results[0] if formatted_results else None

        # Confirmed duplicate if top match >= confirmed_threshold
        if top_match and top_match["score"] >= confirmed_threshold:
            return {"category": "confirmed_duplicate", "confirmed": top_match, "potential": [], "matches": formatted_results}

        # Potential duplicates: any with potential_threshold <= score < confirmed_threshold
        potential = [m for m in formatted_results if m["score"] >= potential_threshold and m["score"] < confirmed_threshold]
        if potential:
            # Return only the highest-scoring potential duplicate
            top_potential = potential[0]  # Already sorted by score in descending order
            return {"category": "potential_duplicate", "confirmed": None, "potential": [top_potential], "matches": formatted_results}

        # Otherwise, new use case
        return {"category": "new_use_case", "confirmed": None, "potential": [], "matches": formatted_results}

    except Exception as e:
        return {"category": "new_use_case", "confirmed": None, "potential": [], "matches": []}

def check_for_duplicates_with_category(use_case_text: str, alpha=0.5, limit=5, confirmed_threshold=0.85, potential_threshold=0.60):
    """
    Check for potential duplicates using hybrid search and LLM-based similarity scoring.
    """
    client = get_weaviate_client()
    openai_client = get_openai_client()
    
    if not client or not use_case_text:
        return {"category": "new_use_case", "confirmed": None, "potential": [], "matches": []}
    
    try:
        # Get the collection
        collection = client.collections.get("UseCase")

        # First, check for exact text matches before doing hybrid search
        # Get all objects and check for exact match manually (more reliable)
        all_objects_response = collection.query.fetch_objects(limit=1000)
        exact_match = None

        for obj in all_objects_response.objects:
            stored_text = obj.properties.get("use_case_text", "").strip()
            if stored_text.lower() == use_case_text.strip().lower():
                exact_match = obj
                break

        # If we found an exact match, return it as confirmed duplicate
        if exact_match:
            exact_result = {
                "use_case_no": exact_match.properties.get("use_case_no", ""),
                "use_case_text": exact_match.properties.get("use_case_text", "").strip(),
                "vector_score": 1.0,  # Perfect match
                "uuid": str(exact_match.uuid) if hasattr(exact_match, 'uuid') else "",
                "score": 1.0,
                "explanation": "The use cases are identical.",
                "word_overlap": 1.0
            }
            return {"category": "confirmed_duplicate", "confirmed": exact_result, "potential": [], "matches": [exact_result]}

        # If no exact match, perform hybrid search to get top matches
        response = collection.query.hybrid(
            query=use_case_text,
            limit=limit,
            alpha=alpha,
            return_metadata=MetadataQuery(score=True)
        )

        if not response.objects:
            return {"category": "new_use_case", "confirmed": None, "potential": [], "matches": []}

        # Format initial results
        formatted_results = []
        for obj in response.objects:
            duplicate_text = obj.properties.get("use_case_text", "").strip()
            if not is_valid_comparison_text(duplicate_text, use_case_text):
                continue
                
            vector_score = 0
            if obj.metadata and obj.metadata.score is not None:
                vector_score = obj.metadata.score
            
            result_item = {
                "use_case_no": obj.properties.get("use_case_no", ""),
                "use_case_text": duplicate_text,
                "vector_score": vector_score,
                "uuid": str(obj.uuid) if hasattr(obj, 'uuid') else ""
            }
            
            # Always use LLM for similarity assessment if available
            if openai_client:
                llm_assessment = assess_similarity_with_llm(
                    openai_client, 
                    use_case_text, 
                    duplicate_text
                )
                result_item.update({
                    "score": llm_assessment["score"],
                    "explanation": llm_assessment["explanation"],
                    "vector_score": vector_score,  # Keep vector score for reference
                    "word_overlap": 0  # Keep for compatibility
                })
            else:
                # Fallback to vector score if LLM is not available
                result_item.update({
                    "score": vector_score,
                    "explanation": "Similarity based on vector search only (LLM not available).",
                    "word_overlap": 0  # Keep for compatibility
                })
            
            formatted_results.append(result_item)
        
        # Sort results by similarity score
        formatted_results.sort(key=lambda x: x["score"], reverse=True)
        
        top_match = formatted_results[0] if formatted_results else None
        
        # Confirmed duplicate if top match >= confirmed_threshold
        if top_match and top_match["score"] >= confirmed_threshold:
            return {"category": "confirmed_duplicate", "confirmed": top_match, "potential": [], "matches": formatted_results}
        
        # Potential duplicates: any with potential_threshold <= score < confirmed_threshold
        potential = [m for m in formatted_results if m["score"] >= potential_threshold and m["score"] < confirmed_threshold]
        if potential:
            # Return only the highest-scoring potential duplicate
            top_potential = potential[0]  # Already sorted by score in descending order
            return {"category": "potential_duplicate", "confirmed": None, "potential": [top_potential], "matches": formatted_results}
        
        # Otherwise, new use case
        return {"category": "new_use_case", "confirmed": None, "potential": [], "matches": formatted_results}

    except Exception as e:
        return {"category": "new_use_case", "confirmed": None, "potential": [], "matches": []}

def generate_use_case_number():
    """Generate a unique use case number - now returns a UUID for consistency"""
    # Generate a proper UUID for use case number to match file upload behavior
    return str(uuid.uuid4())

def add_use_case(use_case_no: str, use_case_text: str, attributes: dict, user_id: str = None):
    client = get_weaviate_client()
    try:
        collection = client.collections.get("UseCase")

        # Auto-generate use_case_no if not provided
        if not use_case_no or use_case_no.strip() == "":
            use_case_no = generate_use_case_number()

        use_case_obj = {
            "use_case_no": str(use_case_no),
            "use_case_text": str(use_case_text),
            "user_id": str(user_id) if user_id else "",
            "applicable_banking_domains": str(attributes.get("applicable_banking_domains", "")),
            "business_value": str(attributes.get("business_value", "")),
            "focus_area": str(attributes.get("focus_area", "")),
            "topline_impact": bool(attributes.get("topline_impact", False)),
            "bottomline_impact": bool(attributes.get("bottomline_impact", False)),
            "regulatory_impact": bool(attributes.get("regulatory_impact", False)),
            "technical_feasibility": int(attributes.get("technical_feasibility", 3)),
            "operational_feasibility": int(attributes.get("operational_feasibility", 3)),
            "business_feasibility": int(attributes.get("business_feasibility", 3)),
            "ai_techniques_used": str(attributes.get("ai_techniques_used", "")),
            "savings_benefit_percentage": float(attributes.get("savings_benefit_percentage", 0)),
            "status": str(attributes.get("status", ""))
        }
        object_uuid = str(uuid.uuid4())
        collection.data.insert(properties=use_case_obj, uuid=object_uuid)
        return True, object_uuid
    except Exception:
        return False, None

def export_use_cases_to_excel():
    client = get_weaviate_client()
    collections = client.collections.list_all()
    collection_names = [c if isinstance(c, str) else getattr(c, 'name', None) for c in collections]
    if "UseCase" not in collection_names:
        return None
    collection = client.collections.get("UseCase")
    response = collection.query.fetch_objects(limit=10000, include_vector=False)
    if not response.objects:
        return None
    data = []
    for obj in response.objects:
        if hasattr(obj, 'properties') and obj.properties:
            props = obj.properties
            topline_impact = "Yes" if props.get("topline_impact", False) else "No"
            bottomline_impact = "Yes" if props.get("bottomline_impact", False) else "No"
            regulatory_impact = "Yes" if props.get("regulatory_impact", False) else "No"
            row = {
                "UUID": str(obj.uuid) if hasattr(obj, 'uuid') else "",
                "User ID": props.get("user_id", ""),
                "Use case": props.get("use_case_text", ""),
                "Applicable Banking Domains": props.get("applicable_banking_domains", ""),
                "Business Value": props.get("business_value", ""),
                "Focus Area": props.get("focus_area", ""),
                "Topline Impact": topline_impact,
                "Bottomline Impact": bottomline_impact,
                "Regulatory Impact": regulatory_impact,
                "Technical Feasibility": props.get("technical_feasibility", ""),
                "Operational Feasibility": props.get("operational_feasibility", ""),
                "Business Feasibility": props.get("business_feasibility", ""),
                "AI Techniques Used": props.get("ai_techniques_used", ""),
                "Approx. % Savings Benefit": props.get("savings_benefit_percentage", 0),
                "Status": props.get("status", "")
            }
            data.append(row)
    df = pd.DataFrame(data)
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, sheet_name='Use Cases', index=False)
        worksheet = writer.sheets['Use Cases']
        for i, col in enumerate(df.columns):
            max_len = max(df[col].astype(str).map(len).max(), len(col)) + 2
            col_width = min(max_len, 50)
            worksheet.set_column(i, i, col_width)
    output.seek(0)
    return output.getvalue()

def export_filtered_use_cases_to_excel(uuids):
    client = get_weaviate_client()
    collections = client.collections.list_all()
    collection_names = [c if isinstance(c, str) else getattr(c, 'name', None) for c in collections]
    if "UseCase" not in collection_names:
        return None
    collection = client.collections.get("UseCase")
    
    # Fetch only the specified use cases
    data = []
    for uuid in uuids:
        try:
            obj = collection.query.fetch_object_by_id(uuid, include_vector=False)
            if obj and hasattr(obj, 'properties') and obj.properties:
                props = obj.properties
                topline_impact = "Yes" if props.get("topline_impact", False) else "No"
                bottomline_impact = "Yes" if props.get("bottomline_impact", False) else "No"
                regulatory_impact = "Yes" if props.get("regulatory_impact", False) else "No"
                row = {
                    "UUID": str(uuid),
                    "User ID": props.get("user_id", ""),
                    "Use case": props.get("use_case_text", ""),
                    "Applicable Banking Domains": props.get("applicable_banking_domains", ""),
                    "Business Value": props.get("business_value", ""),
                    "Focus Area": props.get("focus_area", ""),
                    "Topline Impact": topline_impact,
                    "Bottomline Impact": bottomline_impact,
                    "Regulatory Impact": regulatory_impact,
                    "Technical Feasibility": props.get("technical_feasibility", ""),
                    "Operational Feasibility": props.get("operational_feasibility", ""),
                    "Business Feasibility": props.get("business_feasibility", ""),
                    "AI Techniques Used": props.get("ai_techniques_used", ""),
                    "Approx. % Savings Benefit": props.get("savings_benefit_percentage", 0),
                    "Status": props.get("status", "")
                }
                data.append(row)
        except Exception as e:
            print(f"Error fetching use case {uuid}: {str(e)}")
            continue

    if not data:
        return None

    df = pd.DataFrame(data)
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, sheet_name='Use Cases', index=False)
        worksheet = writer.sheets['Use Cases']
        for i, col in enumerate(df.columns):
            max_len = max(df[col].astype(str).map(len).max(), len(col)) + 2
            col_width = min(max_len, 50)
            worksheet.set_column(i, i, col_width)
    output.seek(0)
    return output.getvalue()

class ExportRequest(BaseModel):
    uuids: List[str]

# --- API Endpoints ---
@app.post("/api/generate-attributes")
def generate_attributes_api(req: UseCaseRequest):
    attributes = generate_attributes(req.use_case_text)
    # Generate use_case_no if not provided
    use_case_no = req.use_case_no if req.use_case_no else generate_use_case_number()
    # For manual entry, we'll return the available status options
    status_options = ["Idea", "Validated", "Demo available"]
    return {
        "attributes": attributes,
        "use_case_no": use_case_no,  # Return the generated or provided use_case_no
        "status_options": status_options  # Frontend can use this to populate the dropdown
    }

@app.post("/api/check-duplicates", response_model=Any)
def check_duplicates_api(req: DuplicateCheckRequest):
    # Check if enhanced search is requested (for manual entry)
    if req.search_strategy == "enhanced":
        # Use dual search strategy for manual entry
        result = check_for_duplicates_enhanced(
            req.use_case_text,
            req.alpha,
            req.limit,
            req.confirmed_threshold,
            req.potential_threshold
        )
    else:
        # Use standard search for file upload and default cases
        result = check_for_duplicates_with_category(
            req.use_case_text,
            req.alpha,
            req.limit,
            req.confirmed_threshold,
            req.potential_threshold
        )
    return result

@app.post("/api/add-use-case")
def add_use_case_api(req: UseCaseRequest):
    # Generate use_case_no if not provided
    use_case_no = req.use_case_no if req.use_case_no else generate_use_case_number()
    success, uuid_value = add_use_case(use_case_no, req.use_case_text, req.attributes.model_dump() if req.attributes else {}, req.user_id)
    return {"success": success, "uuid": uuid_value, "use_case_no": use_case_no}

@app.post("/api/process-file")
def process_file_api(file: UploadFile = File(...)):
    import time
    start_time = time.time()

    try:
        df = pd.read_excel(file.file)
        # Only require "Use case" column, "Use Case No." is now optional
        if "Use case" not in df.columns:
            raise HTTPException(status_code=400, detail="The Excel file must contain a 'Use case' column.")

        # Check if Use Case No. column exists
        has_use_case_no = "Use Case No." in df.columns

        print(f"📊 Processing file with {len(df)} rows")

        results = []
        potential_cases = []
        new_use_cases = []  # Collect new use cases for batch processing

        duplicate_check_time = time.time()

        # First pass: Check for duplicates (fast)
        for _, row in df.iterrows():
            # Get use_case_no if column exists, otherwise it will be auto-generated later
            if has_use_case_no:
                use_case_no = str(row["Use Case No."])
                if use_case_no == "nan" or use_case_no.strip() == "":
                    use_case_no = None
            else:
                use_case_no = None

            use_case_text = str(row["Use case"])

            if pd.isna(use_case_no) or pd.isna(use_case_text) or use_case_text.strip() == "":
                continue
            if len(use_case_text.strip()) < 10 or len(use_case_text.strip().split()) <= 3:
                continue

            # Check against database for duplicates
            dup_result = check_for_duplicates_with_category(use_case_text)
            category = dup_result["category"]

            if category == "confirmed_duplicate":
                top_match = dup_result["confirmed"]
                results.append({
                    "use_case_no": use_case_no,
                    "use_case_text": use_case_text,
                    "duplicate_of": top_match["use_case_no"],
                    "duplicate_text": top_match["use_case_text"],
                    "relevance_score": top_match["score"],
                    "explanation": top_match.get("explanation", "No explanation available"),
                    "vector_score": top_match.get("vector_score", 0),
                    "category": "confirmed_duplicate"
                })
            elif category == "potential_duplicate":
                for pot in dup_result["potential"]:
                    potential_cases.append({
                        "use_case_no": use_case_no,
                        "use_case_text": use_case_text,
                        "potential_duplicate_of": pot["use_case_no"],
                        "potential_duplicate_text": pot["use_case_text"],
                        "relevance_score": pot["score"],
                        "explanation": pot.get("explanation", "No explanation available"),
                        "vector_score": pot.get("vector_score", 0),
                        "category": "potential_duplicate"
                    })
            else:
                # Collect new use cases for batch processing
                new_use_cases.append((use_case_no, use_case_text))

        duplicate_check_duration = time.time() - duplicate_check_time
        print(f"⚡ Duplicate checking completed in {duplicate_check_duration:.2f}s")
        print(f"📈 Found: {len(results)} confirmed duplicates, {len(potential_cases)} potential duplicates, {len(new_use_cases)} new use cases")

        # Second pass: Batch process new use cases (optimized)
        added_count = 0
        if new_use_cases:
            batch_start_time = time.time()
            added_count = process_new_use_cases_batch(new_use_cases)
            batch_duration = time.time() - batch_start_time
            print(f"🚀 Batch processing completed in {batch_duration:.2f}s - Added {added_count} use cases")

        total_duration = time.time() - start_time
        print(f"✅ Total processing time: {total_duration:.2f}s")

        return {
            "results": results,
            "potential": potential_cases,
            "added": added_count,
            "details": {
                "confirmed_duplicates": results,
                "potential_duplicates": potential_cases,
                "added_use_cases": [
                    {
                        "use_case_no": uc[0],
                        "use_case_text": uc[1],
                        "user_id": user_id
                    } for uc in new_use_cases
                ] if new_use_cases else []
            },
            "performance": {
                "total_time": round(total_duration, 2),
                "duplicate_check_time": round(duplicate_check_duration, 2),
                "batch_processing_time": round(batch_duration, 2) if new_use_cases else 0,
                "cache_hits": len([k for k in _attribute_cache.keys()]) if new_use_cases else 0
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing file: {str(e)}")

@app.get("/api/export-use-cases")
@app.post("/api/export-use-cases")
def export_use_cases_api(request: ExportRequest = None):
    if request and request.uuids:
        # Export filtered use cases
        excel_data = export_filtered_use_cases_to_excel(request.uuids)
    else:
        # Export all use cases (existing functionality)
        excel_data = export_use_cases_to_excel()

    if not excel_data:
        raise HTTPException(status_code=404, detail="No use cases found to export.")
    
    headers = {
        'Content-Disposition': 'attachment; filename="use_cases.xlsx"'
    }
    return Response(content=excel_data, media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", headers=headers)

@app.get("/api/get-all-use-cases")
def get_all_use_cases_api():
    """
    Retrieve all use cases from Weaviate database for preview functionality.
    Returns structured data that can be displayed in a table format.
    """
    client = get_weaviate_client()
    try:
        collections = client.collections.list_all()
        collection_names = [c if isinstance(c, str) else getattr(c, 'name', None) for c in collections]
        
        if "UseCase" not in collection_names:
            return {"success": False, "message": "No use cases collection found in the database.", "data": []}
        
        collection = client.collections.get("UseCase")
        response = collection.query.fetch_objects(limit=10000, include_vector=False)
        
        if not response.objects:
            return {"success": True, "message": "No use cases found in the database.", "data": []}
        
        data = []
        for obj in response.objects:
            if hasattr(obj, 'properties') and obj.properties:
                props = obj.properties
                
                # Format boolean values for display
                topline_impact = "Yes" if props.get("topline_impact", False) else "No"
                bottomline_impact = "Yes" if props.get("bottomline_impact", False) else "No"
                regulatory_impact = "Yes" if props.get("regulatory_impact", False) else "No"
                
                # Create a structured record
                record = {
                    "uuid": str(obj.uuid) if hasattr(obj, 'uuid') else "",
                    "use_case_no": props.get("use_case_no", ""),
                    "user_id": props.get("user_id", ""),
                    "use_case_text": props.get("use_case_text", ""),
                    "applicable_banking_domains": props.get("applicable_banking_domains", ""),
                    "business_value": props.get("business_value", ""),
                    "focus_area": props.get("focus_area", ""),
                    "topline_impact": topline_impact,
                    "bottomline_impact": bottomline_impact,
                    "regulatory_impact": regulatory_impact,
                    "technical_feasibility": props.get("technical_feasibility", ""),
                    "operational_feasibility": props.get("operational_feasibility", ""),
                    "business_feasibility": props.get("business_feasibility", ""),
                    "ai_techniques_used": props.get("ai_techniques_used", ""),
                    "savings_benefit_percentage": props.get("savings_benefit_percentage", 0),
                    "status": props.get("status", "")
                }
                data.append(record)
        
        return {
            "success": True, 
            "message": f"Retrieved {len(data)} use cases from the database.", 
            "data": data
        }
        
    except Exception as e:
        return {
            "success": False, 
            "message": f"Error retrieving use cases: {str(e)}", 
            "data": []
        }

@app.post("/api/process-single-use-case")
def process_single_use_case_api(req: SingleUseCaseRequest):
    try:
        # Generate use_case_no if not provided
        use_case_no = req.use_case_no if req.use_case_no else generate_use_case_number()

        # Skip very short or empty use cases
        if not req.use_case_text or len(req.use_case_text.strip()) < 10 or len(req.use_case_text.strip().split()) <= 3:
            return SingleUseCaseResult(
                use_case_no=use_case_no,
                use_case_text=req.use_case_text,
                status="skipped",
                duplicate_info=None,
                uuid=None,
                error="Use case text is too short or empty"
            )

        result = check_for_duplicates_with_category(
            req.use_case_text, 
            req.alpha, 
            5,  # limit
            req.confirmed_threshold, 
            req.potential_threshold
        )
        
        category = result["category"]
        duplicate_info = None
        uuid_value = None
        error = None

        if category == "confirmed_duplicate":
            duplicate_info = result["confirmed"]
            status = "confirmed_duplicate"
        elif category == "potential_duplicate":
            # For potential duplicates, return the first potential match for user review
            duplicate_info = result["potential"][0] if result["potential"] else None
            status = "potential_duplicate"
        elif category == "new_use_case":
            # Automatically generate attributes and add new use case
            attributes = generate_attributes(req.use_case_text)
            # Set initial status as "not set" for all new use cases
            attributes["status"] = "not set"
            success, uuid_value = add_use_case(use_case_no, req.use_case_text, attributes, req.user_id)
            if success:
                status = "added"
            else:
                status = "error"
                error = "Failed to add use case to database"

        return SingleUseCaseResult(
            use_case_no=use_case_no,
            use_case_text=req.use_case_text,
            status=status,
            duplicate_info=duplicate_info,
            uuid=uuid_value,
            error=error
        )
    except Exception as e:
        return SingleUseCaseResult(
            use_case_no=use_case_no if 'use_case_no' in locals() else req.use_case_no,
            use_case_text=req.use_case_text,
            status="error",
            duplicate_info=None,
            uuid=None,
            error=str(e)
        )

@app.post("/api/update-use-case-status")
def update_use_case_status(req: UpdateStatusRequest):
    client = get_weaviate_client()
    try:
        collection = client.collections.get("UseCase")
        collection.data.update(
            uuid=req.uuid,
            properties={
                "status": req.status
            }
        )
        return {"success": True}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating status: {str(e)}")

@app.post("/api/update-use-case")
def update_use_case(req: UpdateUseCaseRequest):
    client = get_weaviate_client()
    try:
        collection = client.collections.get("UseCase")

        # Build properties dict with only non-None values
        properties = {}
        if req.use_case_text is not None:
            properties["use_case_text"] = req.use_case_text
        if req.applicable_banking_domains is not None:
            properties["applicable_banking_domains"] = req.applicable_banking_domains
        if req.business_value is not None:
            properties["business_value"] = req.business_value
        if req.focus_area is not None:
            properties["focus_area"] = req.focus_area
        if req.topline_impact is not None:
            properties["topline_impact"] = req.topline_impact
        if req.bottomline_impact is not None:
            properties["bottomline_impact"] = req.bottomline_impact
        if req.regulatory_impact is not None:
            properties["regulatory_impact"] = req.regulatory_impact
        if req.technical_feasibility is not None:
            properties["technical_feasibility"] = req.technical_feasibility
        if req.operational_feasibility is not None:
            properties["operational_feasibility"] = req.operational_feasibility
        if req.business_feasibility is not None:
            properties["business_feasibility"] = req.business_feasibility
        if req.ai_techniques_used is not None:
            properties["ai_techniques_used"] = req.ai_techniques_used
        if req.savings_benefit_percentage is not None:
            properties["savings_benefit_percentage"] = req.savings_benefit_percentage
        if req.status is not None:
            properties["status"] = req.status

        if not properties:
            raise HTTPException(status_code=400, detail="No fields to update")

        collection.data.update(
            uuid=req.uuid,
            properties=properties
        )
        return {"success": True}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating use case: {str(e)}")

@app.delete("/api/delete-use-case/{uuid}")
def delete_use_case(uuid: str):
    client = get_weaviate_client()
    try:
        collection = client.collections.get("UseCase")

        # First, verify the use case exists
        try:
            use_case = collection.query.fetch_object_by_id(uuid, include_vector=False)
            if not use_case:
                raise HTTPException(status_code=404, detail="Use case not found")
        except Exception:
            raise HTTPException(status_code=404, detail="Use case not found")

        # Delete the use case
        collection.data.delete_by_id(uuid)

        return {"success": True, "message": "Use case deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting use case: {str(e)}")

# Add this endpoint for bulk updates
@app.post("/api/update-multiple-statuses")
def update_multiple_statuses(request: MultipleStatusUpdatesRequest):
    client = get_weaviate_client()
    try:
        collection = client.collections.get("UseCase")
        results = []
        for update in request.updates:
            try:
                collection.data.update(
                    uuid=update.uuid,
                    properties={
                        "status": update.status
                    }
                )
                results.append({"uuid": update.uuid, "success": True})
            except Exception as e:
                results.append({"uuid": update.uuid, "success": False, "error": str(e)})
        return {"results": results}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating statuses: {str(e)}") 