import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import { PublicClientApplication, EventType } from '@azure/msal-browser';
import { msalConfig } from './authconfig';

// Function to capture user information from Microsoft Graph API
const captureUserInfo = async (msalInstance, account) => {
  try {
    // Get access token for Microsoft Graph
    const graphRequest = {
      scopes: ["User.Read", "User.ReadBasic.All"],
      account: account
    };
   
    const response = await msalInstance.acquireTokenSilent(graphRequest);
   
    // Fetch user profile from Microsoft Graph API
    const graphResponse = await fetch('https://graph.microsoft.com/v1.0/me', {
      headers: {
        'Authorization': `Bearer ${response.accessToken}`,
        'Content-Type': 'application/json'
      }
    });
   
    if (!graphResponse.ok) {
      throw new Error(`Graph API request failed: ${graphResponse.status}`);
    }
   
    const userData = await graphResponse.json();
     // Extract user information
     const userInfo = {
      displayName: userData.displayName || account.name || account.username,
      givenName: userData.givenName || '',
      surname: userData.surname || '',
      email: userData.mail || account.username,
      avatar: null
    };
   
    // Try to get user photo
    try {
      const photoResponse = await fetch('https://graph.microsoft.com/v1.0/me/photo/$value', {
        headers: {
          'Authorization': `Bearer ${response.accessToken}`
        }
      });
     
      if (photoResponse.ok) {
        const photoBlob = await photoResponse.blob();
        const photoUrl = URL.createObjectURL(photoBlob);
        userInfo.avatar = photoUrl;
      }
    } catch (photoError) {
      console.log('User photo not available, using default avatar');
    }
   
    // Store user information in session storage
    sessionStorage.setItem('userInfo', JSON.stringify(userInfo));
   
    return userInfo;
  } catch (error) {
    console.error('Error fetching user information:', error);
    // Fallback to basic account information
    const fallbackInfo = {
      displayName: account.name || account.username,
      givenName: '',
      surname: '',
      email: account.username,
      avatar: null
    };
    sessionStorage.setItem('userInfo', JSON.stringify(fallbackInfo));
    return fallbackInfo;
  }
};
 
// Create MSAL instance
const msalInstance = new PublicClientApplication(msalConfig);
 
// Initialize MSAL
const initializeMsal = async () => {
  try {
    console.log('Initializing MSAL instance...');
   
    // Must call initialize before any other MSAL operations
    await msalInstance.initialize();
    console.log('MSAL instance initialized successfully');
   
    // Handle redirect response after initialization
    try {
      const response = await msalInstance.handleRedirectPromise();
     
      if (response) {
        console.log('Redirect response received for user:', response.account?.username);
        msalInstance.setActiveAccount(response.account);
      } else {
        console.log('No redirect response - checking for existing accounts');
        const accounts = msalInstance.getAllAccounts();
       
        if (accounts.length > 0) {
          console.log(`Found ${accounts.length} existing account(s), setting first as active:`, accounts[0].username);
          msalInstance.setActiveAccount(accounts[0]);
         
          // Capture user information for existing account
          try {
            const userInfo = await captureUserInfo(msalInstance, accounts[0]);
            console.log('User information captured for existing account:', userInfo);
          } catch (error) {
            console.error('Error capturing user information for existing account:', error);
          }
        } else {
          console.log('No existing accounts found');
        }
      }
    } catch (redirectError) {
      console.warn('Error handling redirect response:', redirectError);
      // Don't fail initialization just because of redirect handling issues
      // The app can still work for new logins
      if (redirectError.message.includes('AADSTS9002326')) {
        console.error('Azure AD app registration issue: App must be configured as Single-Page Application (SPA)');
        console.error('Please update your Azure AD app registration platform configuration');
      }
    }
   
    // Add event callback for authentication events
    msalInstance.addEventCallback(async (event) => {
      if (process.env.NODE_ENV === 'development') {
        console.log('MSAL Event:', event.eventType);
      }
     
      if (event.eventType === EventType.LOGIN_SUCCESS && event.payload?.account) {
        const account = event.payload.account;
        console.log('Login success - setting active account:', account.username);
        msalInstance.setActiveAccount(account);
       
        // Capture user information after successful login
        try {
          const userInfo = await captureUserInfo(msalInstance, account);
          console.log('User information captured:', userInfo);
        } catch (error) {
          console.error('Error capturing user information:', error);
        }
      }
     
      if (event.eventType === EventType.LOGOUT_SUCCESS) {
        console.log('Logout success');
        msalInstance.setActiveAccount(null);
        // Clear user information from session storage
        sessionStorage.removeItem('userInfo');
      }
    });
   
    console.log('MSAL initialization completed successfully');
    return true;
   
  } catch (error) {
    console.error('MSAL initialization failed:', error);
    console.error('Error details:', {
      name: error.name,
      message: error.message,
      stack: error.stack
    });
    return false;
  }
};
 
// Initialize MSAL and render app
const startApp = async () => {
  const initSuccess = await initializeMsal();
 
  if (!initSuccess) {
    console.error('Failed to initialize MSAL, rendering error page');
    // You could render an error component here instead
    document.getElementById('root').innerHTML = `
      <div style="display: flex; justify-content: center; align-items: center; height: 100vh; flex-direction: column;">
        <h1>Authentication Error</h1>
        <p>Failed to initialize authentication. Please refresh the page.</p>
        <button onclick="window.location.reload()">Refresh Page</button>
      </div>
    `;
    return;
  }

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App Instance={msalInstance} />
  </React.StrictMode>
); 
}

// Start the application
startApp();