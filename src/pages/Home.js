import React from 'react';
import { useNavigate } from 'react-router-dom';
import { FiUpload, FiEdit3, FiEye, FiDownload, FiCpu, FiBarChart2, FiSearch, FiZap } from 'react-icons/fi';

const Home = () => {
  const navigate = useNavigate();

  const tools = [
    {
      title: 'Usecase Repository',
      description: 'View and search through all stored use cases with advanced filtering and export capabilities.',
      icon: FiEye,
      color: '#8b5cf6',
      path: '/preview'
    },
    {
      title: 'File Upload',
      description: 'Upload Excel files with multiple use cases for batch processing and duplicate detection.',
      icon: FiUpload,
      color: '#3b82f6',
      path: '/file-upload'
    },
    {
      title: 'Manual Entry',
      description: 'Enter individual use cases manually with real-time duplicate checking and AI-powered attribute generation.',
      icon: FiEdit3,
      color: '#10b981',
      path: '/manual-entry'
    },
    {
      title: 'Export Data',
      description: 'Export all use cases and their attributes to Excel format for external analysis and reporting.',
      icon: FiDownload,
      color: '#f59e0b',
      path: '/export'
    }
  ];

  return (
    <div>
      <div className="page-header">
        <h1 className="page-title">Welcome to AI Use Case Deduplication Checker</h1>
        
        <p className="page-description">
          <FiCpu style={{ display: 'inline', marginRight: '8px' }} /> <strong>Enhanced with AI:</strong> This system uses advanced AI models to understand semantic similarity between use cases,
          going beyond simple keyword matching to detect true business duplicates. Choose one of the tools below to get started.
        </p>
      </div>

      <div className="dashboard-grid">
        {tools.map((tool, index) => {
          const Icon = tool.icon;
          return (
            <div
              key={index}
              className="dashboard-card"
              onClick={() => navigate(tool.path)}
            >
              <div 
                className="dashboard-card-icon"
                style={{ backgroundColor: tool.color }}
              >
                <Icon />
              </div>
              <h3 className="dashboard-card-title">{tool.title}</h3>
              <p className="dashboard-card-description">{tool.description}</p>
            </div>
          );
        })}
      </div>

      <div style={{ marginTop: '48px' }}>
        <div className="card">
          <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px', color: '#1e293b' }}>
            About This System
          </h2>
          <div style={{ color: '#64748b', lineHeight: '1.6' }}>
            <p style={{ marginBottom: '16px' }}>
              The AI Use Case Deduplication Checker helps organizations maintain a clean, 
              non-redundant database of AI use cases by leveraging advanced semantic similarity detection.
            </p>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '24px', marginTop: '24px' }}>
              <div>
                <h4 style={{ fontWeight: '600', color: '#374151', marginBottom: '8px' }}><FiCpu style={{ display: 'inline', marginRight: '8px' }} /> AI-Powered Detection</h4>
                <p style={{ fontSize: '14px' }}>
                  Uses Azure OpenAI to understand semantic meaning and context, 
                  not just keyword matching.
                </p>
              </div>
              <div>
                <h4 style={{ fontWeight: '600', color: '#374151', marginBottom: '8px' }}><FiBarChart2 style={{ display: 'inline', marginRight: '8px' }} /> Smart Attributes</h4>
                <p style={{ fontSize: '14px' }}>
                  Automatically generates business attributes including feasibility scores, 
                  impact assessments, and technical details.
                </p>
              </div>
              <div>
                <h4 style={{ fontWeight: '600', color: '#374151', marginBottom: '8px' }}><FiSearch style={{ display: 'inline', marginRight: '8px' }} /> Vector Search</h4>
                <p style={{ fontSize: '14px' }}>
                  Utilizes Weaviate vector database for fast, accurate similarity 
                  searches across large datasets.
                </p>
              </div>
              <div>
                <h4 style={{ fontWeight: '600', color: '#374151', marginBottom: '8px' }}><FiZap style={{ display: 'inline', marginRight: '8px' }} /> Real-time Processing</h4>
                <p style={{ fontSize: '14px' }}>
                  Instant duplicate detection and attribute generation 
                  for efficient workflow management.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home; 