import React, { useState, useCallback } from 'react';
import axios from 'axios';
import { <PERSON><PERSON>ye, FiSearch, FiDownload, FiRefreshCw, FiEdit2, FiCheck, FiTrash2, FiX, FiBarChart2, FiAlertTriangle, FiClipboard } from 'react-icons/fi';

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';

const STATUS_OPTIONS = ["Idea", "Validated", "Demo available"];
const IMPACT_OPTIONS = [
  { value: true, label: "Yes" },
  { value: false, label: "No" }
];
const FEASIBILITY_OPTIONS = [
  { value: 1, label: "1 (High)" },
  { value: 2, label: "2" },
  { value: 3, label: "3" },
  { value: 4, label: "4" },
  { value: 5, label: "5 (Low)" }
];

const Preview = () => {
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    focus_area: 'All',
    topline_impact: 'All',
    bottomline_impact: 'All',
    regulatory_impact: 'All',
    status: 'All'  // Add status filter
  });
  const [editingRow, setEditingRow] = useState(null); // UUID of the row being edited
  const [editedData, setEditedData] = useState({}); // Track all field changes: { uuid: { field: newValue } }
  const [deleteConfirmation, setDeleteConfirmation] = useState(null); // { uuid } for delete confirmation

  const [saveError, setSaveError] = useState('');

  const loadData = async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${BACKEND_URL}/api/get-all-use-cases`);
      if (response.data.success) {
        setData(response.data.data);
        setFilteredData(response.data.data);
      } else {
        console.error('Failed to load data:', response.data.message);
      }
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = useCallback(() => {
    let filtered = [...data];

    // Text search
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(item => {
        return (
          item.uuid?.toLowerCase().includes(searchLower) ||
          item.user_id?.toLowerCase().includes(searchLower) ||
          item.use_case_text?.toLowerCase().includes(searchLower) ||
          item.applicable_banking_domains?.toLowerCase().includes(searchLower) ||
          item.business_value?.toLowerCase().includes(searchLower) ||
          item.focus_area?.toLowerCase().includes(searchLower) ||
          item.ai_techniques_used?.toLowerCase().includes(searchLower)
        );
      });
    }

    // Focus area filter
    if (filters.focus_area !== 'All') {
      filtered = filtered.filter(item => item.focus_area === filters.focus_area);
    }

    // Impact filters
    if (filters.topline_impact !== 'All') {
      filtered = filtered.filter(item => item.topline_impact === filters.topline_impact);
    }

    if (filters.bottomline_impact !== 'All') {
      filtered = filtered.filter(item => item.bottomline_impact === filters.bottomline_impact);
    }

    if (filters.regulatory_impact !== 'All') {
      filtered = filtered.filter(item => item.regulatory_impact === filters.regulatory_impact);
    }

    // Add status filter
    if (filters.status !== 'All') {
      filtered = filtered.filter(item => {
        const currentStatus = editedData[item.uuid]?.status || item.status;
        return currentStatus === filters.status;
      });
    }

    setFilteredData(filtered);
  }, [data, searchTerm, filters, editedData]);

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  React.useEffect(() => {
    applyFilters();
  }, [applyFilters]);

  const exportFilteredData = async () => {
    try {
      // Get UUIDs of filtered use cases
      const filteredUuids = filteredData.map(item => item.uuid);
      
      // Create a download link and trigger it
      const response = await axios.post(
        `${BACKEND_URL}/api/export-use-cases`,
        { uuids: filteredUuids },
        { responseType: 'blob' }  // Important for handling file download
      );
      
      // Generate filename with current date
      const now = new Date();
      const dateStr = now.toISOString().split('T')[0];
      
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `filtered_use_cases_${dateStr}.xlsx`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting data:', error);
      // You might want to show an error message to the user here
    }
  };

  const getFocusAreas = () => {
    const areas = [...new Set(data.map(item => item.focus_area).filter(Boolean))];
    return areas.sort();
  };

  const handleEditRow = (uuid) => {
    setEditingRow(uuid);
    // Initialize edited data with current values, converting display strings back to proper types
    const currentItem = data.find(item => item.uuid === uuid);
    if (currentItem) {
      const editableData = {
        ...currentItem,
        // Convert "Yes"/"No" strings back to booleans for editing
        topline_impact: currentItem.topline_impact === "Yes",
        bottomline_impact: currentItem.bottomline_impact === "Yes",
        regulatory_impact: currentItem.regulatory_impact === "Yes",
        // Ensure feasibility fields are numbers (in case they come as strings)
        technical_feasibility: typeof currentItem.technical_feasibility === 'string' ?
          (currentItem.technical_feasibility === '' ? null : parseInt(currentItem.technical_feasibility)) :
          currentItem.technical_feasibility,
        operational_feasibility: typeof currentItem.operational_feasibility === 'string' ?
          (currentItem.operational_feasibility === '' ? null : parseInt(currentItem.operational_feasibility)) :
          currentItem.operational_feasibility,
        business_feasibility: typeof currentItem.business_feasibility === 'string' ?
          (currentItem.business_feasibility === '' ? null : parseInt(currentItem.business_feasibility)) :
          currentItem.business_feasibility,
        // Ensure savings percentage is a number
        savings_benefit_percentage: typeof currentItem.savings_benefit_percentage === 'string' ?
          (currentItem.savings_benefit_percentage === '' ? null : parseFloat(currentItem.savings_benefit_percentage)) :
          currentItem.savings_benefit_percentage
      };

      setEditedData(prev => ({
        ...prev,
        [uuid]: editableData
      }));
    }
  };

  const handleFieldChange = (uuid, field, value) => {
    setEditedData(prev => ({
      ...prev,
      [uuid]: {
        ...prev[uuid],
        [field]: value
      }
    }));
  };

  const handleSaveRow = async (uuid) => {
    try {
      const editedFields = editedData[uuid];
      if (!editedFields) return;

      // Prepare the update payload with only changed fields
      const updatePayload = {
        uuid,
        ...editedFields
      };

      await axios.post(`${BACKEND_URL}/api/update-use-case`, updatePayload);

      // Convert boolean values back to "Yes"/"No" strings for display
      const displayFields = { ...editedFields };

      // Convert impact boolean fields to display format
      if ('topline_impact' in displayFields) {
        displayFields.topline_impact = displayFields.topline_impact ? 'Yes' : 'No';
      }
      if ('bottomline_impact' in displayFields) {
        displayFields.bottomline_impact = displayFields.bottomline_impact ? 'Yes' : 'No';
      }
      if ('regulatory_impact' in displayFields) {
        displayFields.regulatory_impact = displayFields.regulatory_impact ? 'Yes' : 'No';
      }

      // Update local data with display-formatted values
      setData(prevData => prevData.map(item =>
        item.uuid === uuid ? { ...item, ...displayFields } : item
      ));
      setFilteredData(prevData => prevData.map(item =>
        item.uuid === uuid ? { ...item, ...displayFields } : item
      ));

      // Remove this item from editedData since it's saved
      setEditedData(prev => {
        const newData = { ...prev };
        delete newData[uuid];
        return newData;
      });

      setEditingRow(null);
      setSaveError('');
    } catch (error) {
      console.error('Error saving use case:', error);
      setSaveError('Failed to save use case. Please try again.');
    }
  };

  const handleDeleteUseCase = (uuid) => {
    // Show custom confirmation modal
    setDeleteConfirmation({ uuid });
  };

  const confirmDelete = async () => {
    if (!deleteConfirmation) return;

    const { uuid } = deleteConfirmation;

    try {
      await axios.delete(`${BACKEND_URL}/api/delete-use-case/${uuid}`);

      // Remove from local data
      setData(prevData => prevData.filter(item => item.uuid !== uuid));
      setFilteredData(prevData => prevData.filter(item => item.uuid !== uuid));

      // Clean up any editing states for this item
      setEditedData(prev => {
        const newData = { ...prev };
        delete newData[uuid];
        return newData;
      });

      if (editingRow === uuid) {
        setEditingRow(null);
      }

      // Close modal
      setDeleteConfirmation(null);
    } catch (error) {
      console.error('Error deleting use case:', error);
      alert('Failed to delete use case. Please try again.');
    }
  };

  const cancelDelete = () => {
    setDeleteConfirmation(null);
  };

  const cancelEdit = (uuid) => {
    setEditingRow(null);
    setEditedData(prev => {
      const newData = { ...prev };
      delete newData[uuid];
      return newData;
    });
  };

  // Helper function to render editable text field
  const renderEditableText = (uuid, field, value, isTextarea = false) => {
    const editedValue = editedData[uuid]?.[field] ?? value;

    if (editingRow === uuid) {
      if (isTextarea) {
        return (
          <textarea
            value={editedValue || ''}
            onChange={(e) => handleFieldChange(uuid, field, e.target.value)}
            className="form-input"
            style={{ minHeight: '60px', resize: 'vertical' }}
          />
        );
      } else {
        return (
          <input
            type="text"
            value={editedValue || ''}
            onChange={(e) => handleFieldChange(uuid, field, e.target.value)}
            className="form-input"
          />
        );
      }
    }
    return <span>{value}</span>;
  };

  // Helper function to render editable select field
  const renderEditableSelect = (uuid, field, value, options) => {
    const editedValue = editedData[uuid]?.[field] ?? value;

    if (editingRow === uuid) {
      return (
        <select
          value={editedValue || ''}
          onChange={(e) => handleFieldChange(uuid, field, e.target.value)}
          className="form-select"
        >
          <option value="">Select...</option>
          {options.map(option => (
            <option key={option} value={option}>
              {option}
            </option>
          ))}
        </select>
      );
    }
    return <span>{value}</span>;
  };

  // Helper function to render editable boolean field
  const renderEditableBoolean = (uuid, field, value) => {
    const editedValue = editedData[uuid]?.[field] ?? value;

    if (editingRow === uuid) {
      return (
        <select
          value={editedValue ? 'true' : 'false'}
          onChange={(e) => handleFieldChange(uuid, field, e.target.value === 'true')}
          className="form-select"
        >
          {IMPACT_OPTIONS.map(option => (
            <option key={option.value.toString()} value={option.value.toString()}>
              {option.label}
            </option>
          ))}
        </select>
      );
    }
    return <span>{value ? 'Yes' : 'No'}</span>;
  };

  // Helper function to render editable number field
  const renderEditableNumber = (uuid, field, value, options = null) => {
    const editedValue = editedData[uuid]?.[field] ?? value;

    if (editingRow === uuid) {
      if (options) {
        return (
          <select
            value={editedValue || ''}
            onChange={(e) => handleFieldChange(uuid, field, parseInt(e.target.value))}
            className="form-select"
          >
            <option value="">Select...</option>
            {options.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      } else {
        return (
          <input
            type="number"
            value={editedValue || ''}
            onChange={(e) => handleFieldChange(uuid, field, parseFloat(e.target.value))}
            className="form-input"
            step="0.1"
          />
        );
      }
    }
    return <span>{value}</span>;
  };

  return (
    <div>
      <div className="page-header">
        <h1 className="page-title"><FiEye style={{ display: 'inline', marginRight: '8px' }} /> Preview All Use Cases</h1>
        <p className="page-description">
          View and search through all use cases stored in the database with advanced filtering capabilities.
        </p>
      </div>

      {/* Load Data Section */}
      <div className="card mb-6">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h3 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '8px', color: '#1e293b' }}>
              Database Contents
            </h3>
            <p style={{ color: '#64748b', margin: 0 }}>
              Load all use cases from the database to view and search
            </p>
          </div>
          <button
            onClick={loadData}
            className="btn-primary"
            disabled={loading}
          >
            <FiRefreshCw style={{ marginRight: '8px' }} />
            {loading ? 'Loading...' : 'Load All Use Cases'}
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      {data.length > 0 && (
        <div className="table-container">
          <div className="table-header">
            <h3 className="table-title">Use Cases Database</h3>
          </div>
          
          <div className="search-filters">
            <div className="search-grid">
              <div className="form-group" style={{ margin: 0 }}>
                <label className="form-label"><FiSearch style={{ display: 'inline', marginRight: '8px' }} /> Search in all fields</label>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="form-input"
                  placeholder="Search by UUID, User ID, description, domains, or other fields..."
                />
              </div>
              
              <div className="form-group" style={{ margin: 0 }}>
                <label className="form-label">Filter by Focus Area</label>
                <select
                  name="focus_area"
                  value={filters.focus_area}
                  onChange={handleFilterChange}
                  className="form-select"
                >
                  <option value="All">All</option>
                  {getFocusAreas().map(area => (
                    <option key={area} value={area}>{area}</option>
                  ))}
                </select>
              </div>
            </div>

            <div className="filter-grid">
              <div className="form-group" style={{ margin: 0 }}>
                <label className="form-label">Topline Impact</label>
                <select
                  name="topline_impact"
                  value={filters.topline_impact}
                  onChange={handleFilterChange}
                  className="form-select"
                >
                  <option value="All">All</option>
                  <option value="Yes">Yes</option>
                  <option value="No">No</option>
                </select>
              </div>
              
              <div className="form-group" style={{ margin: 0 }}>
                <label className="form-label">Bottomline Impact</label>
                <select
                  name="bottomline_impact"
                  value={filters.bottomline_impact}
                  onChange={handleFilterChange}
                  className="form-select"
                >
                  <option value="All">All</option>
                  <option value="Yes">Yes</option>
                  <option value="No">No</option>
                </select>
              </div>
              
              <div className="form-group" style={{ margin: 0 }}>
                <label className="form-label">Regulatory Impact</label>
                <select
                  name="regulatory_impact"
                  value={filters.regulatory_impact}
                  onChange={handleFilterChange}
                  className="form-select"
                >
                  <option value="All">All</option>
                  <option value="Yes">Yes</option>
                  <option value="No">No</option>
                </select>
              </div>

              <div className="form-group" style={{ margin: 0 }}>
                <label className="form-label">Status</label>
                <select
                  name="status"
                  value={filters.status}
                  onChange={handleFilterChange}
                  className="form-select"
                >
                  <option value="All">All</option>
                  {STATUS_OPTIONS.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          <div className="results-info">
            <FiBarChart2 style={{ display: 'inline', marginRight: '8px' }} /> Showing {filteredData.length} of {data.length} use cases
            {filteredData.length > 0 && (
              <button
                onClick={exportFilteredData}
                className="btn-secondary"
                style={{ marginLeft: '16px', padding: '6px 12px', fontSize: '12px' }}
              >
                <FiDownload style={{ marginRight: '6px' }} />
                Export Results
              </button>
            )}
          </div>

          {/* Show error message if any */}
          {saveError && (
            <div className="card mb-4">
              <div className="alert alert-error">
                {saveError}
              </div>
            </div>
          )}

          {/* Data Table */}
          {filteredData.length > 0 ? (
            <div className="table-scroll" style={{
              overflowX: 'auto',
              overflowY: 'auto',
              maxHeight: '600px',
              border: '1px solid #e2e8f0',
              borderRadius: '6px'
            }}>
              <table className="table" style={{
                borderCollapse: 'collapse',
                width: 'auto'
              }}>
                <thead>
                  <tr>
                    <th style={{ position: 'sticky', top: 0, backgroundColor: '#f8fafc', zIndex: 1, minWidth: '120px', borderRight: '1px solid #e5e7eb' }}>UUID</th>
                    <th style={{ position: 'sticky', top: 0, backgroundColor: '#f8fafc', zIndex: 1, minWidth: '120px', borderRight: '1px solid #e5e7eb' }}>User ID</th>
                    <th style={{ position: 'sticky', top: 0, backgroundColor: '#f8fafc', zIndex: 1, minWidth: '600px', borderRight: '1px solid #e5e7eb' }}>Description</th>
                    <th style={{ position: 'sticky', top: 0, backgroundColor: '#f8fafc', zIndex: 1, minWidth: '150px', borderRight: '1px solid #e5e7eb' }}>Focus Area</th>
                    <th style={{ position: 'sticky', top: 0, backgroundColor: '#f8fafc', zIndex: 1, minWidth: '250px', borderRight: '1px solid #e5e7eb' }}>Banking Domains</th>
                    <th style={{ position: 'sticky', top: 0, backgroundColor: '#f8fafc', zIndex: 1, minWidth: '300px', borderRight: '1px solid #e5e7eb' }}>Business Value</th>
                    <th style={{ position: 'sticky', top: 0, backgroundColor: '#f8fafc', zIndex: 1, minWidth: '100px', borderRight: '1px solid #e5e7eb' }}>Impacts</th>
                    <th style={{ position: 'sticky', top: 0, backgroundColor: '#f8fafc', zIndex: 1, minWidth: '120px', borderRight: '1px solid #e5e7eb' }}>Feasibility</th>
                    <th style={{ position: 'sticky', top: 0, backgroundColor: '#f8fafc', zIndex: 1, minWidth: '250px', borderRight: '1px solid #e5e7eb' }}>AI Techniques</th>
                    <th style={{ position: 'sticky', top: 0, backgroundColor: '#f8fafc', zIndex: 1, minWidth: '100px', borderRight: '1px solid #e5e7eb' }}>Savings %</th>
                    <th style={{ position: 'sticky', top: 0, backgroundColor: '#f8fafc', zIndex: 1, minWidth: '100px', borderRight: '1px solid #e5e7eb' }}>Status</th>
                    <th style={{ position: 'sticky', top: 0, backgroundColor: '#f8fafc', zIndex: 1, minWidth: '100px' }}>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredData.map((item) => (
                    <tr key={item.uuid}>
                      <td style={{
                        fontWeight: '500',
                        fontFamily: 'monospace',
                        fontSize: '12px',
                        color: '#6b7280',
                        width: '120px',
                        minWidth: '120px',
                        maxWidth: '120px',
                        wordBreak: 'break-all',
                        whiteSpace: 'normal',
                        lineHeight: '1.3',
                        padding: '12px',
                        verticalAlign: 'middle',
                        textAlign: 'left',
                        borderRight: '1px solid #e5e7eb'
                      }} title={item.uuid}>
                        <div style={{
                          display: '-webkit-box',
                          WebkitLineClamp: 3,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          maxHeight: '48px'
                        }}>
                          {item.uuid}
                        </div>
                      </td>
                      <td style={{
                        fontWeight: '500',
                        fontSize: '14px',
                        color: '#374151',
                        width: '120px',
                        minWidth: '120px',
                        maxWidth: '120px',
                        padding: '12px',
                        verticalAlign: 'middle',
                        textAlign: 'left',
                        borderRight: '1px solid #e5e7eb'
                      }}>
                        {item.user_id || '-'}
                      </td>
                      <td style={{
                        minWidth: '600px',
                        padding: '12px',
                        verticalAlign: 'middle',
                        borderRight: '1px solid #e5e7eb'
                      }}>
                        <div style={{
                          whiteSpace: 'normal',
                          wordBreak: 'break-word',
                          lineHeight: '1.5'
                        }}>
                          {renderEditableText(item.uuid, 'use_case_text', item.use_case_text, true)}
                        </div>
                      </td>
                      <td style={{
                        padding: '12px',
                        verticalAlign: 'middle',
                        borderRight: '1px solid #e5e7eb'
                      }}>
                        {renderEditableText(item.uuid, 'focus_area', item.focus_area)}
                      </td>
                      <td style={{
                        minWidth: '250px',
                        padding: '12px',
                        verticalAlign: 'middle',
                        borderRight: '1px solid #e5e7eb'
                      }}>
                        <div style={{
                          whiteSpace: 'normal',
                          wordBreak: 'break-word'
                        }}>
                          {renderEditableText(item.uuid, 'applicable_banking_domains', item.applicable_banking_domains, true)}
                        </div>
                      </td>
                      <td style={{
                        minWidth: '300px',
                        padding: '12px',
                        verticalAlign: 'middle',
                        borderRight: '1px solid #e5e7eb'
                      }}>
                        <div style={{
                          whiteSpace: 'normal',
                          wordBreak: 'break-word',
                          lineHeight: '1.4'
                        }}>
                          {renderEditableText(item.uuid, 'business_value', item.business_value, true)}
                        </div>
                      </td>
                      <td style={{
                        padding: '12px',
                        verticalAlign: 'middle',
                        borderRight: '1px solid #e5e7eb'
                      }}>
                        {editingRow === item.uuid ? (
                          <div style={{ fontSize: '12px', display: 'flex', flexDirection: 'column', gap: '4px' }}>
                            <div>T: {renderEditableBoolean(item.uuid, 'topline_impact', item.topline_impact === "Yes")}</div>
                            <div>B: {renderEditableBoolean(item.uuid, 'bottomline_impact', item.bottomline_impact === "Yes")}</div>
                            <div>R: {renderEditableBoolean(item.uuid, 'regulatory_impact', item.regulatory_impact === "Yes")}</div>
                          </div>
                        ) : (
                          <div style={{ fontSize: '12px' }}>
                            <div>T: {item.topline_impact}</div>
                            <div>B: {item.bottomline_impact}</div>
                            <div>R: {item.regulatory_impact}</div>
                          </div>
                        )}
                      </td>
                      <td style={{
                        padding: '12px',
                        verticalAlign: 'middle',
                        borderRight: '1px solid #e5e7eb'
                      }}>
                        {editingRow === item.uuid ? (
                          <div style={{ fontSize: '12px', display: 'flex', flexDirection: 'column', gap: '4px' }}>
                            <div>Tech: {renderEditableNumber(item.uuid, 'technical_feasibility', item.technical_feasibility, FEASIBILITY_OPTIONS)}</div>
                            <div>Ops: {renderEditableNumber(item.uuid, 'operational_feasibility', item.operational_feasibility, FEASIBILITY_OPTIONS)}</div>
                            <div>Biz: {renderEditableNumber(item.uuid, 'business_feasibility', item.business_feasibility, FEASIBILITY_OPTIONS)}</div>
                          </div>
                        ) : (
                          <div style={{ fontSize: '12px' }}>
                            <div>Tech: {item.technical_feasibility}</div>
                            <div>Ops: {item.operational_feasibility}</div>
                            <div>Biz: {item.business_feasibility}</div>
                          </div>
                        )}
                      </td>
                      <td style={{
                        minWidth: '250px',
                        padding: '12px',
                        verticalAlign: 'middle',
                        borderRight: '1px solid #e5e7eb'
                      }}>
                        <div style={{
                          whiteSpace: 'normal',
                          wordBreak: 'break-word',
                          fontSize: '12px'
                        }}>
                          {renderEditableText(item.uuid, 'ai_techniques_used', item.ai_techniques_used, true)}
                        </div>
                      </td>
                      <td style={{ textAlign: 'center', fontWeight: '500', padding: '12px', borderRight: '1px solid #e5e7eb' }}>
                        {renderEditableNumber(item.uuid, 'savings_benefit_percentage', item.savings_benefit_percentage)}
                        {editingRow !== item.uuid && '%'}
                      </td>
                      <td style={{ padding: '12px', borderRight: '1px solid #e5e7eb' }}>
                        {renderEditableSelect(item.uuid, 'status', item.status || 'Not Set', STATUS_OPTIONS)}
                      </td>
                      <td style={{ padding: '12px' }}>
                        <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                          {editingRow === item.uuid ? (
                            <>
                              <button
                                onClick={() => handleSaveRow(item.uuid)}
                                className="btn-icon"
                                title="Save Changes"
                                style={{ color: '#059669' }}
                              >
                                <FiCheck />
                              </button>
                              <button
                                onClick={() => cancelEdit(item.uuid)}
                                className="btn-icon"
                                title="Cancel Edit"
                                style={{ color: '#6b7280' }}
                              >
                                <FiX />
                              </button>
                            </>
                          ) : (
                            <button
                              onClick={() => handleEditRow(item.uuid)}
                              className="btn-icon"
                              title="Edit Row"
                            >
                              <FiEdit2 />
                            </button>
                          )}
                          <button
                            onClick={() => handleDeleteUseCase(item.uuid)}
                            className="btn-icon"
                            title="Delete Use Case"
                            style={{
                              color: '#dc2626'
                            }}
                          >
                            <FiTrash2 />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div style={{ padding: '48px', textAlign: 'center', color: '#64748b' }}>
              <FiAlertTriangle style={{ display: 'inline', marginRight: '8px' }} /> No use cases match the current filter criteria.
            </div>
          )}
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {deleteConfirmation && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '8px',
            padding: '24px',
            maxWidth: '400px',
            width: '90%',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
          }}>
            <h3 style={{
              fontSize: '18px',
              fontWeight: '600',
              color: '#1e293b',
              marginBottom: '12px'
            }}>
              Delete Use Case
            </h3>
            <p style={{
              color: '#64748b',
              marginBottom: '8px',
              lineHeight: '1.5'
            }}>
              Are you sure you want to delete the use case with UUID <strong>{deleteConfirmation.uuid}</strong>?
            </p>
            <p style={{
              color: '#dc2626',
              fontSize: '14px',
              marginBottom: '24px',
              fontWeight: '500'
            }}>
              This action cannot be undone.
            </p>
            <div style={{
              display: 'flex',
              gap: '12px',
              justifyContent: 'flex-end'
            }}>
              <button
                onClick={cancelDelete}
                style={{
                  padding: '8px 16px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  backgroundColor: 'white',
                  color: '#374151',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = '#f9fafb';
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = 'white';
                }}
              >
                Cancel
              </button>
              <button
                onClick={confirmDelete}
                style={{
                  padding: '8px 16px',
                  border: 'none',
                  borderRadius: '6px',
                  backgroundColor: '#dc2626',
                  color: 'white',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = '#b91c1c';
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = '#dc2626';
                }}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Empty State */}
      {data.length === 0 && !loading && (
        <div className="card" style={{ textAlign: 'center', padding: '48px' }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}><FiClipboard size={48} /></div>
          <h3 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '8px', color: '#64748b' }}>
            No Data Loaded
          </h3>
          <p style={{ color: '#64748b', marginBottom: '24px' }}>
            Click "Load All Use Cases" to fetch data from the database.
          </p>
        </div>
      )}
    </div>
  );
};

export default Preview; 