import React, { useState } from 'react';
import axios from 'axios';
import { FiDownload, FiCheck, FiUpload, FiBarChart2, FiClipboard, FiFileText, FiBriefcase, FiSun, FiTarget, FiCpu } from 'react-icons/fi';

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';

const Export = () => {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');

  const handleExport = async () => {
    setLoading(true);
    setError('');
    setSuccess(false);

    try {
      const response = await axios.get(`${BACKEND_URL}/api/export-use-cases`, {
        responseType: 'blob'
      });

      // Create blob link to download
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;

      // Generate filename with current date
      const now = new Date();
      const dateStr = now.toISOString().split('T')[0];
      link.setAttribute('download', `ai_use_cases_export_${dateStr}.xlsx`);

      // Append to html link element page
      document.body.appendChild(link);

      // Start download
      link.click();

      // Clean up and remove the link
      link.parentNode.removeChild(link);

      setSuccess(true);
    } catch (error) {
      console.error('Error exporting data:', error);
      setError(error.response?.data?.detail || 'Failed to export use cases. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div className="page-header">
        <h1 className="page-title"><FiUpload style={{ display: 'inline', marginRight: '8px' }} /> Export Use Cases</h1>
        <p className="page-description">
          Export all use cases and their attributes to Excel format for external analysis and reporting.
        </p>
      </div>

      <div className="card">
        <div style={{ textAlign: 'center', padding: '24px' }}>
          <div style={{ fontSize: '64px', marginBottom: '24px' }}>
            <FiBarChart2 size={64} />
          </div>
          
          <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px', color: '#1e293b' }}>
            Excel Export
          </h3>
          
          <p style={{ color: '#64748b', marginBottom: '32px', maxWidth: '500px', margin: '0 auto 32px' }}>
            Generate a comprehensive Excel file containing all use cases with their complete attributes, 
            including business value, feasibility scores, AI techniques, and impact assessments.
          </p>

          {error && (
            <div className="alert alert-error" style={{ marginBottom: '24px' }}>
              {error}
            </div>
          )}

          {success && (
            <div className="alert alert-success" style={{ marginBottom: '24px' }}>
              <FiCheck style={{ marginRight: '8px' }} />
              Export completed successfully! Your download should start automatically.
            </div>
          )}

          <button
            onClick={handleExport}
            className="btn-primary"
            disabled={loading}
            style={{ 
              padding: '16px 32px', 
              fontSize: '16px',
              minWidth: '200px'
            }}
          >
            {loading ? (
              <>
                <div className="spinner" style={{ 
                  width: '16px', 
                  height: '16px', 
                  marginRight: '8px',
                  borderWidth: '2px'
                }} />
                Generating Export...
              </>
            ) : (
              <>
                <FiDownload style={{ marginRight: '8px' }} />
                Generate Excel Export
              </>
            )}
          </button>
        </div>
      </div>

      {/* Export Details */}
      <div style={{ marginTop: '32px' }}>
        <div className="card">
          <h3 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '16px', color: '#1e293b' }}>
            <FiClipboard style={{ display: 'inline', marginRight: '8px' }} /> What's Included in the Export
          </h3>
          
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' }}>
            <div>
              <h4 style={{ fontSize: '16px', fontWeight: '600', color: '#374151', marginBottom: '8px' }}>
                <FiFileText style={{ display: 'inline', marginRight: '8px' }} /> Basic Information
              </h4>
              <ul style={{ color: '#64748b', fontSize: '14px', lineHeight: '1.6', margin: 0, paddingLeft: '20px' }}>
                <li>Use Case Number</li>
                <li>User ID</li>
                <li>Use Case Description</li>
                <li>Focus Area</li>
                <li>Applicable Banking Domains</li>
              </ul>
            </div>
            
            <div>
              <h4 style={{ fontSize: '16px', fontWeight: '600', color: '#374151', marginBottom: '8px' }}>
                <FiBriefcase style={{ display: 'inline', marginRight: '8px' }} /> Business Attributes
              </h4>
              <ul style={{ color: '#64748b', fontSize: '14px', lineHeight: '1.6', margin: 0, paddingLeft: '20px' }}>
                <li>Business Value</li>
                <li>Topline Impact</li>
                <li>Bottomline Impact</li>
                <li>Regulatory Impact</li>
              </ul>
            </div>
            
            <div>
              <h4 style={{ fontSize: '16px', fontWeight: '600', color: '#374151', marginBottom: '8px' }}>
                <FiTarget style={{ display: 'inline', marginRight: '8px' }} /> Feasibility Scores
              </h4>
              <ul style={{ color: '#64748b', fontSize: '14px', lineHeight: '1.6', margin: 0, paddingLeft: '20px' }}>
                <li>Technical Feasibility (1-5)</li>
                <li>Operational Feasibility (1-5)</li>
                <li>Business Feasibility (1-5)</li>
                <li>Savings Benefit Percentage</li>
              </ul>
            </div>
            
            <div>
              <h4 style={{ fontSize: '16px', fontWeight: '600', color: '#374151', marginBottom: '8px' }}>
                <FiCpu style={{ display: 'inline', marginRight: '8px' }} /> AI & Technology
              </h4>
              <ul style={{ color: '#64748b', fontSize: '14px', lineHeight: '1.6', margin: 0, paddingLeft: '20px' }}>
                <li>AI Techniques Used</li>
                <li>Technology Requirements</li>
                <li>Implementation Complexity</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Export Tips */}
      <div style={{ marginTop: '24px' }}>
        <div className="card" style={{ background: '#f8fafc', border: '1px solid #e2e8f0' }}>
          <h3 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '12px', color: '#1e293b' }}>
            <FiSun style={{ display: 'inline', marginRight: '8px' }} /> Export Tips
          </h3>
          <ul style={{ color: '#64748b', fontSize: '14px', lineHeight: '1.8', margin: 0, paddingLeft: '20px' }}>
            <li>The Excel file will be formatted with proper column widths for easy reading</li>
            <li>All data is exported in a single sheet called "Use Cases"</li>
            <li>Boolean values are displayed as "Yes" or "No" for clarity</li>
            <li>The file can be opened in Excel, Google Sheets, or any spreadsheet application</li>
            <li>Use this export for reporting, analysis, or sharing with stakeholders</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default Export; 