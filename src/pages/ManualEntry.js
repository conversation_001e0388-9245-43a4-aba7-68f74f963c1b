import React, { useState } from 'react';
import axios from 'axios';
import { FiEdit3, FiCheck, FiX, FiAlertTriangle, FiCpu, FiFileText } from 'react-icons/fi';
import { useMsal } from '@azure/msal-react';

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';

const ManualEntry = () => {
  const { accounts } = useMsal();
  const [formData, setFormData] = useState({
    use_case_text: ''
  });

  // Extract user ID from email
  const getUserId = () => {
    if (accounts && accounts.length > 0) {
      const email = accounts[0].username;
      return email.split('@')[0]; // Extract part before @
    }
    return null;
  };
  const [stage, setStage] = useState('input'); // input, checking, potential, confirmed, attributes, success
  const [loading, setLoading] = useState(false);
  const [duplicateInfo, setDuplicateInfo] = useState(null);
  const [potentialDuplicates, setPotentialDuplicates] = useState([]);
  const [attributes, setAttributes] = useState(null);
  const [statusOptions, setStatusOptions] = useState([]);  // Add this line
  const [error, setError] = useState('');
  const [statusMessage, setStatusMessage] = useState('');

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAttributeChange = (e) => {
    const { name, value, type, checked } = e.target;
    setAttributes(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : type === 'number' ? Number(value) : value
    }));
  };

  const checkForDuplicates = async () => {
    console.log('🚀 ManualEntry: checkForDuplicates function called!');
    console.log('🚀 ManualEntry: formData.use_case_text:', formData.use_case_text);
    console.log('🚀 ManualEntry: BACKEND_URL:', BACKEND_URL);

    if (!formData.use_case_text) {
      setError('Please enter a use case description.');
      return;
    }

    setLoading(true);
    setError('');
    setStage('checking');

    try {
      console.log('🔍 ManualEntry: Checking duplicates for:', formData.use_case_text);

      const response = await axios.post(`${BACKEND_URL}/api/check-duplicates`, {
        use_case_text: formData.use_case_text,
        search_strategy: "enhanced"  // Use enhanced search for manual entry
      }, {
        timeout: 30000 // 30 seconds timeout for duplicate checking
      });

      const result = response.data;
      console.log('🔍 ManualEntry: Duplicate check result:', result);
      console.log('🔍 ManualEntry: Result category:', result.category);
      console.log('🔍 ManualEntry: Full result object:', JSON.stringify(result, null, 2));

      if (result.category === 'confirmed_duplicate') {
        console.log('✅ ManualEntry: Found confirmed duplicate, setting stage to confirmed');
        setDuplicateInfo(result.confirmed);
        setStage('confirmed');
      } else if (result.category === 'potential_duplicate') {
        console.log('⚠️ ManualEntry: Found potential duplicates, setting stage to potential');
        setPotentialDuplicates(result.potential);
        setStage('potential');
      } else {
        console.log('🆕 ManualEntry: No duplicates found, generating attributes');
        // No duplicates, generate attributes
        await generateAttributes();
      }
    } catch (error) {
      console.error('❌ ManualEntry: Error checking duplicates:', error);
      console.error('❌ ManualEntry: Error details:', {
        message: error.message,
        code: error.code,
        response: error.response,
        request: error.request
      });

      if (error.code === 'ECONNABORTED') {
        setError('Duplicate checking is taking longer than expected. Please try again.');
      } else if (error.response) {
        setError(`Server error: ${error.response.status}. Please try again.`);
      } else if (error.request) {
        setError('Unable to connect to server. Please check your connection and try again.');
      } else {
        setError('Error checking for duplicates. Please try again.');
      }
      setStage('input');
    } finally {
      setLoading(false);
    }
  };

  const generateAttributes = async () => {
    console.log('🔧 ManualEntry: Starting attribute generation');
    setLoading(true);
    setStatusMessage('Processing use case... This may take a few seconds.');

    try {
      const response = await axios.post(`${BACKEND_URL}/api/generate-attributes`, {
        use_case_text: formData.use_case_text
      });

      setStatusMessage('✅ Use case processed successfully!');
      await new Promise(resolve => setTimeout(resolve, 1000));

      console.log('🔧 ManualEntry: Attributes generated, setting stage to attributes');
      setAttributes(response.data.attributes);
      setStatusOptions(response.data.status_options); // Add this line
      setStage('attributes');
      setStatusMessage('');
    } catch (error) {
      console.error('Error generating attributes:', error);
      setError('Error generating attributes. Please try again.');
      setStage('input');
      setStatusMessage('❌ Error generating attributes. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handlePotentialDuplicateResponse = (isDuplicate) => {
    if (isDuplicate) {
      setStage('confirmed');
    } else {
      generateAttributes();
    }
  };

  const submitUseCase = async () => {
    setLoading(true);
    setStatusMessage('Processing use case... This may take a few seconds.');

    try {
      const userId = getUserId();

      const response = await axios.post(`${BACKEND_URL}/api/add-use-case`, {
        use_case_text: formData.use_case_text,
        user_id: userId,
        attributes: attributes
      });

      if (response.data.success) {
        setStatusMessage('✅ Use case added successfully!');
        await new Promise(resolve => setTimeout(resolve, 1000));
        setStage('success');
        setStatusMessage('');
      } else {
        setError('Failed to add use case. Please try again.');
        setStatusMessage('❌ Failed to add use case. Please try again.');
      }
    } catch (error) {
      console.error('Error adding use case:', error);
      setError('Error adding use case. Please try again.');
      setStatusMessage('❌ Error adding use case. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({ use_case_text: '' });
    setStage('input');
    setDuplicateInfo(null);
    setPotentialDuplicates([]);
    setAttributes(null);
    setError('');
    setStatusMessage('');
  };

  return (
    <div>
      <div className="page-header">
        <h1 className="page-title"><FiEdit3 style={{ display: 'inline', marginRight: '8px' }} /> Manual Use Case Entry</h1>
        <p className="page-description">
          Enter individual use cases manually with real-time duplicate checking and AI-powered attribute generation.
        </p>
      </div>

      {error && (
        <div className="alert alert-error">
          {error}
        </div>
      )}



      {stage === 'input' && (
        <div className="card">
          <h3 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '20px', color: '#1e293b' }}>
            Enter Use Case Details
          </h3>
          
          <div className="form-group">
            <label className="form-label">Use Case Description</label>
            <textarea
              name="use_case_text"
              value={formData.use_case_text}
              onChange={handleInputChange}
              className="form-textarea"
              placeholder="Enter detailed description of the use case..."
              style={{ minHeight: '150px' }}
            />
          </div>

          <button
            onClick={checkForDuplicates}
            className="btn-primary"
            disabled={loading}
            style={{ width: '100%' }}
          >
            <FiEdit3 style={{ marginRight: '8px' }} />
            Check Use Case
          </button>
        </div>
      )}

      {stage === 'checking' && (
        <div className="card" style={{ textAlign: 'center', padding: '48px' }}>
          <div className="spinner" style={{ margin: '0 auto 16px' }} />
          <p><FiCpu style={{ display: 'inline', marginRight: '8px' }} /> AI is analyzing semantic similarity for duplicates...</p>
        </div>
      )}

      {stage === 'confirmed' && (
        <div className="card">
          <div style={{
            background: '#fef3c7',
            border: '1px solid #fde68a',
            padding: '16px',
            borderRadius: '8px',
            marginBottom: '16px'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
              <FiAlertTriangle style={{ marginRight: '8px', fontSize: '20px', color: '#92400e' }} />
              <h3 style={{ fontSize: '16px', fontWeight: '600', margin: 0, color: '#92400e' }}>
                Confirmed Duplicate Detected!
              </h3>
            </div>
            
            {duplicateInfo && (
              <div>
                <p style={{ marginBottom: '12px', color: '#92400e' }}>
                  <strong>This use case is a confirmed duplicate of existing use case #{duplicateInfo.uuid}:</strong>
                </p>

                <div style={{ color: '#78350f', fontSize: '14px', lineHeight: '1.5' }}>
                  <div style={{ marginBottom: '8px' }}>
                    <strong>Existing Use Case:</strong> {duplicateInfo.use_case_text}
                  </div>
                  <div style={{ marginBottom: '8px' }}>
                    <strong>Your Use Case:</strong> {formData.use_case_text}
                  </div>
                </div>

                <div style={{ fontSize: '14px', color: '#92400e' }}>
                  <div style={{ fontWeight: '500', marginBottom: '4px' }}>AI Analysis:</div>
                  <div style={{ fontSize: '13px' }}>
                    <FiCpu style={{ display: 'inline', marginRight: '8px' }} /> AI Similarity Score: {(duplicateInfo.score * 100).toFixed(1)}%
                    {duplicateInfo.explanation && (
                      <div style={{ marginTop: '4px' }}>
                        <FiFileText style={{ display: 'inline', marginRight: '8px' }} /> {duplicateInfo.explanation}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
          
          <p style={{ color: '#64748b', marginTop: '16px', marginBottom: '20px' }}>
            Addition cancelled. This is a confirmed duplicate.
          </p>
          
          <button onClick={resetForm} className="btn-primary">
            Start New Entry
          </button>
        </div>
      )}

      {stage === 'potential' && (
        <div className="card">
          <div style={{
            background: '#f3e8ff',
            border: '1px solid #e9d5ff',
            padding: '16px',
            borderRadius: '8px',
            marginBottom: '16px'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
              <FiAlertTriangle style={{ marginRight: '8px', fontSize: '20px', color: '#7c3aed' }} />
              <h3 style={{ fontSize: '16px', fontWeight: '600', margin: 0, color: '#7c3aed' }}>
                Potential Duplicate(s) Detected!
              </h3>
            </div>
            <p style={{ color: '#7c3aed', margin: 0 }}>Please confirm if any are duplicates.</p>
          </div>

          {potentialDuplicates.map((potential, index) => (
            <div key={index} style={{
              background: '#f3e8ff',
              border: '1px solid #e9d5ff',
              borderRadius: '8px',
              padding: '16px',
              marginTop: '16px'
            }}>
              <div style={{
                fontSize: '16px',
                fontWeight: '600',
                color: '#7c3aed',
                marginBottom: '8px'
              }}>
                <FiCpu style={{ display: 'inline', marginRight: '8px' }} /> Potential Duplicate (AI Score: {(potential.score * 100).toFixed(1)}%)
              </div>
              <div style={{ color: '#6b21a8', fontSize: '14px', lineHeight: '1.5' }}>
                <div style={{ marginBottom: '8px' }}>
                  <strong>Potential duplicate of use case #{potential.uuid}:</strong>
                </div>
                <div style={{ marginBottom: '8px' }}>
                  <strong>Existing Use Case:</strong> {potential.use_case_text}
                </div>
                <div style={{ marginBottom: '8px' }}>
                  <strong>Your Use Case:</strong> {formData.use_case_text}
                </div>
                {potential.explanation && (
                  <div style={{ fontSize: '12px', marginBottom: '12px' }}>
                    <FiFileText style={{ display: 'inline', marginRight: '8px' }} /> AI Analysis: {potential.explanation}
                  </div>
                )}

                <div style={{
                  marginTop: '16px',
                  display: 'flex',
                  gap: '12px'
                }}>
                  <button
                    onClick={() => handlePotentialDuplicateResponse(true)}
                    className="btn-secondary"
                    style={{
                      flex: 1,
                      backgroundColor: '#fef3c7',
                      color: '#92400e',
                      border: 'none'
                    }}
                  >
                    <FiX style={{ marginRight: '6px' }} />
                    Yes, it's a duplicate
                  </button>
                  <button
                    onClick={() => handlePotentialDuplicateResponse(false)}
                    className="btn-primary"
                    style={{
                      flex: 1,
                      backgroundColor: '#dcfce7',
                      color: 'white',
                      border: 'none'
                    }}
                  >
                    <FiCheck style={{ marginRight: '6px' }} />
                    No, it's different
                  </button>
                </div>
              </div>
            </div>
          ))}

          <div style={{ marginTop: '20px' }}>
            <button onClick={resetForm} className="btn-secondary">
              Start New Entry
            </button>
          </div>
        </div>
      )}

      {stage === 'attributes' && attributes && (
        <div className="attributes-form">
          <h3 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '20px', color: '#1e293b' }}>
            Review and Edit Generated Attributes
          </h3>
          
          <div className="attributes-grid">
            <div className="form-group">
              <label className="form-label">Applicable Banking Domains</label>
              <input
                type="text"
                name="applicable_banking_domains"
                value={attributes.applicable_banking_domains}
                onChange={handleAttributeChange}
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label className="form-label">Focus Area</label>
              <input
                type="text"
                name="focus_area"
                value={attributes.focus_area}
                onChange={handleAttributeChange}
                className="form-input"
              />
            </div>

            <div className="form-group attributes-full-width">
              <label className="form-label">Business Value</label>
              <textarea
                name="business_value"
                value={attributes.business_value}
                onChange={handleAttributeChange}
                className="form-textarea"
                style={{ minHeight: '100px' }}
              />
            </div>

            <div className="form-group">
              <label className="form-label">Status</label>
              <select
                name="status"
                value={attributes.status}
                onChange={handleAttributeChange}
                className="form-select"
              >
                <option value="">Select Status</option>
                {statusOptions.map((option) => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label className="form-label">Topline Impact</label>
              <select
                name="topline_impact"
                value={attributes.topline_impact}
                onChange={handleAttributeChange}
                className="form-select"
              >
                <option value={true}>Yes</option>
                <option value={false}>No</option>
              </select>
            </div>

            <div className="form-group">
              <label className="form-label">Bottomline Impact</label>
              <select
                name="bottomline_impact"
                value={attributes.bottomline_impact}
                onChange={handleAttributeChange}
                className="form-select"
              >
                <option value={true}>Yes</option>
                <option value={false}>No</option>
              </select>
            </div>

            <div className="form-group">
              <label className="form-label">Regulatory Impact</label>
              <select
                name="regulatory_impact"
                value={attributes.regulatory_impact}
                onChange={handleAttributeChange}
                className="form-select"
              >
                <option value={true}>Yes</option>
                <option value={false}>No</option>
              </select>
            </div>

            <div className="form-group">
              <label className="form-label">Technical Feasibility (1-5)</label>
              <input
                type="number"
                name="technical_feasibility"
                value={attributes.technical_feasibility}
                onChange={handleAttributeChange}
                className="form-input"
                min="1"
                max="5"
              />
            </div>

            <div className="form-group">
              <label className="form-label">Operational Feasibility (1-5)</label>
              <input
                type="number"
                name="operational_feasibility"
                value={attributes.operational_feasibility}
                onChange={handleAttributeChange}
                className="form-input"
                min="1"
                max="5"
              />
            </div>

            <div className="form-group">
              <label className="form-label">Business Feasibility (1-5)</label>
              <input
                type="number"
                name="business_feasibility"
                value={attributes.business_feasibility}
                onChange={handleAttributeChange}
                className="form-input"
                min="1"
                max="5"
              />
            </div>

            <div className="form-group attributes-full-width">
              <label className="form-label">AI Techniques Used</label>
              <input
                type="text"
                name="ai_techniques_used"
                value={attributes.ai_techniques_used}
                onChange={handleAttributeChange}
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label className="form-label">Savings Benefit Percentage</label>
              <input
                type="number"
                name="savings_benefit_percentage"
                value={attributes.savings_benefit_percentage}
                onChange={handleAttributeChange}
                className="form-input"
                step="0.1"
              />
            </div>
          </div>

          <div style={{ marginTop: '24px', display: 'flex', gap: '12px' }}>
            <button
              onClick={submitUseCase}
              className="btn-primary"
              disabled={loading}
              style={{ flex: 1 }}
            >
              <FiCheck style={{ marginRight: '8px' }} />
              Confirm and Save Use Case
            </button>
            <button
              onClick={resetForm}
              className="btn-secondary"
            >
              Start New Entry
            </button>
          </div>
        </div>
      )}

      {stage === 'success' && (
        <div className="card" style={{ textAlign: 'center', padding: '48px' }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>✅</div>
          <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px', color: '#10b981' }}>
            Use Case Added Successfully!
          </h3>
          <p style={{ color: '#64748b', marginBottom: '24px' }}>
            Your use case has been saved to the database with all generated attributes.
          </p>
          <button onClick={resetForm} className="btn-primary">
            Add Another Use Case
          </button>
        </div>
      )}

      {statusMessage && (
        <div style={{
          marginTop: '24px',
          padding: '12px',
          backgroundColor: statusMessage.includes('❌') ? '#fee2e2' : '#d1fae5',
          color: statusMessage.includes('❌') ? '#991b1b' : '#065f46',
          borderRadius: '6px',
          textAlign: 'center',
          fontWeight: '500',
          border: `2px solid ${statusMessage.includes('❌') ? '#fca5a5' : '#86efac'}`
        }}>
          {statusMessage}
        </div>
      )}
    </div>
  );
};

export default ManualEntry; 