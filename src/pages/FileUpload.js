import React, { useState, useCallback } from 'react';
import axios from 'axios';
import { FiUpload, FiFile, FiX, FiAlertTriangle, FiEye, FiPlay, FiPause, FiBarChart2, FiClipboard, FiCheck } from 'react-icons/fi';
import * as XLSX from 'xlsx';
import { useMsal } from '@azure/msal-react';

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';

const AddedUseCaseCard = ({ useCase }) => {
  const [expanded, setExpanded] = useState(false);

  return (
    <div className="added-use-case-card">
      <div
        className="card-header"
        onClick={() => setExpanded(!expanded)}
        style={{ cursor: 'pointer', padding: '12px', backgroundColor: '#dcfce7', borderRadius: '6px' }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <span style={{ fontWeight: '600', color: '#16a34a' }}>
              File Use Case #{useCase?.file_position}
            </span>
          </div>
          <div>
            <FiCheck style={{ color: '#16a34a' }} />
          </div>
        </div>
      </div>

      {expanded && (
        <div className="card-content" style={{ padding: '12px', backgroundColor: '#fff', borderRadius: '0 0 6px 6px' }}>
          <div style={{ marginBottom: '12px' }}>
            <div style={{ fontWeight: '500', marginBottom: '4px' }}>Your Use Case:</div>
            <div style={{ padding: '8px', backgroundColor: '#f8fafc', borderRadius: '4px' }}>
              {useCase?.use_case_text || 'No text available'}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

const PotentialDuplicateCard = ({
  useCase,
  onConfirmDuplicate,
  onConfirmNew
}) => {
  const [expanded, setExpanded] = useState(false);

  // Safely access duplicate info
  const duplicateInfo = useCase?.duplicate_info || {};
  const score = duplicateInfo.score || 0;
  const useCaseUuid = duplicateInfo.uuid || 'Unknown';
  const useCaseText = duplicateInfo.use_case_text || 'No text available';
  const explanation = duplicateInfo.explanation || 'No explanation available';

  return (
    <div className="potential-duplicate-card">
      <div 
        className="card-header" 
        onClick={() => setExpanded(!expanded)}
        style={{ cursor: 'pointer', padding: '12px', backgroundColor: '#f3e8ff', borderRadius: '6px' }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <span style={{ fontWeight: '600', color: '#7c3aed' }}>
              File Use Case #{useCase?.file_position || 'Pending'}
            </span>
            <FiAlertTriangle style={{ color: '#7c3aed', marginLeft: '8px' }} />
          </div>
          <div style={{ fontSize: '14px', color: '#7c3aed' }}>
            Score: {(score * 100).toFixed(1)}%
          </div>
        </div>
      </div>
      
      {expanded && (
        <div className="card-content" style={{ padding: '12px', backgroundColor: '#fff', borderRadius: '0 0 6px 6px' }}>
          <div style={{ marginBottom: '12px' }}>
            <div style={{ fontWeight: '500', marginBottom: '4px' }}>Your Use Case:</div>
            <div style={{ padding: '8px', backgroundColor: '#f8fafc', borderRadius: '4px' }}>
              {useCase?.use_case_text || 'No text available'}
            </div>
          </div>
          
          <div style={{ marginBottom: '12px' }}>
            <div style={{ fontWeight: '500', marginBottom: '4px' }}>Potential Duplicate ({useCaseUuid}):</div>
            <div style={{ padding: '8px', backgroundColor: '#f8fafc', borderRadius: '4px' }}>
              {useCaseText}
            </div>
          </div>
          
          <div style={{ marginBottom: '12px' }}>
            <div style={{ fontWeight: '500', marginBottom: '4px' }}>AI Analysis:</div>
            <div style={{ padding: '8px', backgroundColor: '#f8fafc', borderRadius: '4px', fontSize: '14px' }}>
              {explanation}
            </div>
          </div>
          
          <div style={{ display: 'flex', gap: '8px', marginTop: '16px' }}>
            <button
              onClick={() => onConfirmDuplicate(useCase)}
              className="btn-secondary"
              style={{ flex: 1, backgroundColor: '#fef3c7', color: '#92400e', border: 'none' }}
            >
              Mark as Duplicate
            </button>
            <button
              onClick={() => onConfirmNew(useCase)}
              className="btn-primary"
              style={{ flex: 1 }}
            >
              Add as New
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

const ConfirmedDuplicateCard = ({ result }) => {
  const [expanded, setExpanded] = useState(false);

  return (
    <div className="confirmed-duplicate-card">
      <div 
        className="card-header" 
        onClick={() => setExpanded(!expanded)}
        style={{ cursor: 'pointer', padding: '12px', backgroundColor: '#fef3c7', borderRadius: '6px' }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <span style={{ fontWeight: '600', color: '#92400e' }}>
              File Use Case #{result.file_position || result.use_case_no} is a duplicate of {result.duplicate_uuid || result.duplicate_of}
            </span>
            <FiAlertTriangle style={{ color: '#92400e', marginLeft: '8px' }} />
          </div>
          <div style={{ fontSize: '14px', color: '#92400e' }}>
            Score: {(result.relevance_score * 100).toFixed(1)}%
          </div>
        </div>
      </div>
      
      {expanded && (
        <div className="card-content" style={{ padding: '12px', backgroundColor: '#fff', borderRadius: '0 0 6px 6px' }}>
          <div style={{ marginBottom: '12px' }}>
            <div style={{ fontWeight: '500', marginBottom: '4px' }}>Your Use Case:</div>
            <div style={{ padding: '8px', backgroundColor: '#f8fafc', borderRadius: '4px' }}>
              {result.use_case_text || 'No text available'}
            </div>
          </div>
          
          <div style={{ marginBottom: '12px' }}>
            <div style={{ fontWeight: '500', marginBottom: '4px' }}>Existing Use Case:</div>
            <div style={{ padding: '8px', backgroundColor: '#f8fafc', borderRadius: '4px' }}>
              {result.duplicate_text || 'No text available'}
            </div>
          </div>
          
          <div style={{ marginBottom: '12px' }}>
            <div style={{ fontWeight: '500', marginBottom: '4px' }}>AI Analysis:</div>
            <div style={{ padding: '8px', backgroundColor: '#f8fafc', borderRadius: '4px', fontSize: '14px' }}>
              {result.explanation || 'No explanation available'}
            </div>
          </div>
          
          {result.vector_score !== undefined && (
            <div style={{ fontSize: '12px', color: '#64748b' }}>
              <FiBarChart2 style={{ display: 'inline', marginRight: '8px' }} /> Vector Search Score: {(result.vector_score * 100).toFixed(1)}%
            </div>
          )}
        </div>
      )}
    </div>
  );
};

const FileUpload = () => {
  const { accounts } = useMsal();
  const [file, setFile] = useState(null);

  // Extract user ID from email
  const getUserId = () => {
    if (accounts && accounts.length > 0) {
      const email = accounts[0].username;
      return email.split('@')[0]; // Extract part before @
    }
    return null;
  };
  const [filePreview, setFilePreview] = useState(null);

  const [processing, setProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [results, setResults] = useState(null);
  const [currentStatus, setCurrentStatus] = useState('');
  const [dragOver, setDragOver] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [processingResults, setProcessingResults] = useState({
    confirmed_duplicates: [],
    potential_duplicates: [],
    added: [],
    skipped: [],
    errors: []
  });
  const [currentUseCaseIndex, setCurrentUseCaseIndex] = useState(0);
  const [totalUseCases, setTotalUseCases] = useState(0);
  const [stopProcessing, setStopProcessing] = useState(false);
  const [abortController, setAbortController] = useState(null);
  const [showPotentialDuplicates, setShowPotentialDuplicates] = useState(false);
  const [pendingPotentialDuplicates, setPendingPotentialDuplicates] = useState([]);
  const [successMessage, setSuccessMessage] = useState('');


  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    setDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    setDragOver(false);
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setDragOver(false);
    const droppedFile = e.dataTransfer.files[0];
    if (droppedFile && (droppedFile.name.endsWith('.xlsx') || droppedFile.name.endsWith('.xls'))) {
      setFile(droppedFile);
      loadFilePreview(droppedFile);
    } else {
      alert('Please upload only Excel files (.xlsx or .xls)');
    }
  }, []);

  const handleFileSelect = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile) {
      setFile(selectedFile);
      loadFilePreview(selectedFile);
    }
  };

  const loadFilePreview = (file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        if (jsonData.length > 0) {
          const headers = jsonData[0];
          const rows = jsonData.slice(1);
          
          // Check if required column exists (exact match)
          const hasUseCase = headers.some(header =>
            header && header.toString().trim() === 'Use case'
          );

          if (!hasUseCase) {
            const foundHeaders = headers.filter(h => h).map(h => `"${h}"`).join(', ');
            alert(`The Excel file must contain a "Use case" column.\n\nFound columns: ${foundHeaders}\n\nPlease ensure your Excel file has the correct column name (case-sensitive).`);
            setFile(null);
            setFilePreview(null);
            return;
          }
          
          setFilePreview({
            headers,
            rows: rows, // Show all rows for preview
            totalRows: rows.length
          });
        }
      } catch (error) {
        console.error('Error reading file:', error);
        alert('Error reading the Excel file. Please make sure it\'s a valid Excel file.');
        setFile(null);
        setFilePreview(null);
      }
    };
    reader.readAsArrayBuffer(file);
  };

  const removeFile = () => {
    setFile(null);
    setFilePreview(null);
    setResults(null);
    setProgress(0);
    setCurrentStatus('');
    setShowPreview(false);
    setProcessingResults({
      confirmed_duplicates: [],
      potential_duplicates: [],
      added: [],
      skipped: [],
      errors: []
    });
    setCurrentUseCaseIndex(0);
    setTotalUseCases(0);
    setStopProcessing(false);
    setAbortController(null);
    setPendingPotentialDuplicates([]);
    setShowPotentialDuplicates(false);
    setSuccessMessage('');
  };

  const handleConfirmDuplicate = async (useCase) => {
    const updatedPending = pendingPotentialDuplicates.filter(
      p => p.unique_id !== useCase.unique_id
    );
    setPendingPotentialDuplicates(updatedPending);

    // Update results to move this to confirmed duplicates
    setProcessingResults(prev => ({
      ...prev,
      confirmed_duplicates: [...prev.confirmed_duplicates, {
        use_case_no: useCase.use_case_no,
        use_case_text: useCase.use_case_text,
        file_position: useCase.file_position,
        duplicate_of: useCase.duplicate_info.use_case_no,
        duplicate_uuid: useCase.duplicate_info.uuid,
        duplicate_text: useCase.duplicate_info.use_case_text,
        relevance_score: useCase.duplicate_info.score,
        explanation: useCase.duplicate_info.explanation,
        vector_score: useCase.duplicate_info.vector_score
      }],
      potential_duplicates: prev.potential_duplicates.filter(
        p => p.unique_id !== useCase.unique_id
      )
    }));
  };

  const handleConfirmNew = async (useCase) => {
    try {
      // Show loading state for this specific use case
      setSuccessMessage('Processing use case... This may take a few seconds.');

      // Generate attributes for the use case
      const attrResponse = await axios.post(`${BACKEND_URL}/api/generate-attributes`, {
        use_case_text: useCase.use_case_text
      });

      if (attrResponse.data.attributes) {
        // Add status as "not set" to the attributes
        const attributes = {
          ...attrResponse.data.attributes,
          status: "not set"
        };

        // Add the use case with generated attributes
        const userId = getUserId();
        const addResponse = await axios.post(`${BACKEND_URL}/api/add-use-case`, {
          use_case_text: useCase.use_case_text,
          user_id: userId,
          attributes: attributes
        });

        if (addResponse.data.success) {
          const updatedPending = pendingPotentialDuplicates.filter(
            p => p.unique_id !== useCase.unique_id
          );
          setPendingPotentialDuplicates(updatedPending);

          // Update results
          setProcessingResults(prev => ({
            ...prev,
            added: [...prev.added, {
              use_case_no: useCase.use_case_no,
              use_case_text: useCase.use_case_text,
              uuid: addResponse.data.uuid,
              status: "not set"
            }],
            potential_duplicates: prev.potential_duplicates.filter(
              p => p.unique_id !== useCase.unique_id
            )
          }));

          // Show persistent success message
          setSuccessMessage('✅ Use Case Added Successfully! Your use case has been saved to the database with all generated attributes.');
        }
      }
    } catch (error) {
      console.error('Error adding new use case:', error);
      setSuccessMessage('❌ Error adding use case. Please try again.');
      // Add to errors list
      setProcessingResults(prev => ({
        ...prev,
        errors: [...prev.errors, {
          use_case_no: useCase.use_case_no,
          error: `Failed to add as new: ${error.message}`
        }]
      }));
    }
  };

  const handleAddAllRemaining = async () => {
    if (pendingPotentialDuplicates.length === 0) return;

    setProcessing(true);
    const useCasesToProcess = [...pendingPotentialDuplicates]; // Create a copy
    const addedUseCases = [];
    let successCount = 0;
    let errorCount = 0;

    // Show progress message
    setSuccessMessage(`Processing ${useCasesToProcess.length} use cases in parallel... This may take a minute.`);

    try {
      // Process use cases in parallel for better performance
      const processingPromises = useCasesToProcess.map(async (useCase, index) => {
        try {
          // Update progress
          setSuccessMessage(`Processing ${useCasesToProcess.length} use cases... Please wait.`);

          // Generate attributes for the use case
          const attrResponse = await axios.post(`${BACKEND_URL}/api/generate-attributes`, {
            use_case_text: useCase.use_case_text
          });

          if (attrResponse.data.attributes) {
            // Add status as "not set" to the attributes
            const attributes = {
              ...attrResponse.data.attributes,
              status: "not set"
            };

            // Add the use case with generated attributes
            const userId = getUserId();
            const addResponse = await axios.post(`${BACKEND_URL}/api/add-use-case`, {
              use_case_text: useCase.use_case_text,
              user_id: userId,
              attributes: attributes
            });

            if (addResponse.data.success) {
              return {
                success: true,
                useCase: {
                  use_case_no: useCase.use_case_no,
                  use_case_text: useCase.use_case_text,
                  uuid: addResponse.data.uuid,
                  status: "not set"
                }
              };
            } else {
              return { success: false, error: 'Failed to add to database' };
            }
          } else {
            return { success: false, error: 'Failed to generate attributes' };
          }
        } catch (error) {
          console.error(`Error processing use case ${useCase.use_case_no}:`, error);
          return { success: false, error: error.message };
        }
      });

      // Wait for all processing to complete
      const results = await Promise.all(processingPromises);

      // Process results
      results.forEach(result => {
        if (result.success) {
          addedUseCases.push(result.useCase);
          successCount++;
        } else {
          errorCount++;
        }
      });

      // Update state after all processing is complete
      setPendingPotentialDuplicates([]); // Clear all pending duplicates

      // Update results with all added use cases
      setProcessingResults(prev => ({
        ...prev,
        added: [...prev.added, ...addedUseCases],
        potential_duplicates: prev.potential_duplicates.filter(
          p => !useCasesToProcess.some(processed => processed.use_case_no === p.use_case_no)
        )
      }));

      // Show persistent success message
      if (successCount > 0) {
        setSuccessMessage(`✅ Successfully added ${successCount} use case${successCount > 1 ? 's' : ''} to the database!${errorCount > 0 ? ` ⚠️ ${errorCount} failed to process.` : ''}`);
      } else {
        setSuccessMessage('❌ Failed to add any use cases. Please try again.');
      }

    } catch (error) {
      console.error('Error in bulk processing:', error);
      setSuccessMessage('❌ An error occurred while processing use cases. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const processFile = async () => {
    if (!file || !filePreview) return;

    setProcessing(true);
    setProgress(0);
    setResults(null);
    setCurrentStatus('Reading file data...');
    setStopProcessing(false);

    // Create abort controller for cancellation
    const controller = new AbortController();
    setAbortController(controller);

    try {
      // Read the full file data first
      const fileData = await readFileData(file);

      // Find the "Use case" column index
      const useCaseColumnIndex = fileData.headers.findIndex(header =>
        header && header.toString().trim() === 'Use case'
      );

      // Find the "Use Case No." column index (optional)
      const useCaseNoColumnIndex = fileData.headers.findIndex(header =>
        header && header.toString().trim() === 'Use Case No.'
      );

      const useCases = fileData.rows.filter(row =>
        row.length > useCaseColumnIndex &&
        row[useCaseColumnIndex] &&
        row[useCaseColumnIndex].toString().trim() !== ''
      );

      setTotalUseCases(useCases.length);
      setCurrentUseCaseIndex(0);
      setCurrentStatus(`Found ${useCases.length} use cases to process...`);

      const results = {
        confirmed_duplicates: [],
        potential_duplicates: [],
        added: [],
        skipped: [],
        errors: []
      };

      // Process each use case individually to show real-time progress
      for (let i = 0; i < useCases.length; i++) {
        if (stopProcessing) {
          setCurrentStatus('Processing stopped by user');
          break;
        }

        const row = useCases[i];
        const useCaseText = row[useCaseColumnIndex] ? row[useCaseColumnIndex].toString().trim() : '';
        const useCaseNo = useCaseNoColumnIndex >= 0 && row[useCaseNoColumnIndex] ?
          row[useCaseNoColumnIndex].toString().trim() : null;

        if (!useCaseText) continue;

        setCurrentUseCaseIndex(i + 1);
        setCurrentStatus(`Processing Use Case ${i + 1} of ${useCases.length}: ${useCaseNo || 'Auto-generated'}`);
        // Set progress to show current processing step (not completion)
        setProgress((i / useCases.length) * 100);

        try {
          // Use the batch processing endpoint for better performance
          const userId = getUserId();
          const response = await axios.post(`${BACKEND_URL}/api/process-single-use-case`, {
            use_case_no: useCaseNo,
            use_case_text: useCaseText,
            user_id: userId,
            alpha: 0.5,
            confirmed_threshold: 0.85,
            potential_threshold: 0.60
          }, {
            timeout: 60000, // 1 minute timeout per use case
            signal: controller.signal // Add abort signal
          });

          const result = response.data;
          const status = result.status;

          if (status === "confirmed_duplicate") {
            results.confirmed_duplicates.push({
              use_case_no: useCaseNo,
              use_case_text: useCaseText,
              file_position: i + 1,
              duplicate_of: result.duplicate_info.use_case_no,
              duplicate_uuid: result.duplicate_info.uuid,
              duplicate_text: result.duplicate_info.use_case_text,
              relevance_score: result.duplicate_info.score,
              explanation: result.duplicate_info.explanation,
              vector_score: result.duplicate_info.vector_score
            });
          } else if (status === "potential_duplicate") {
            results.potential_duplicates.push({
              use_case_no: useCaseNo,
              use_case_text: useCaseText,
              file_position: i + 1,
              duplicate_info: result.duplicate_info,
              unique_id: `${useCaseNo || 'null'}_${i}_${useCaseText.substring(0, 50)}`
            });
          } else if (status === "added") {
            results.added.push({
              use_case_no: useCaseNo,
              use_case_text: useCaseText,
              file_position: i + 1,
              uuid: result.uuid
            });
          } else if (status === "skipped") {
            results.skipped.push({
              use_case_no: useCaseNo,
              reason: result.error
            });
          } else if (status === "error") {
            results.errors.push({
              use_case_no: useCaseNo,
              error: result.error
            });
          } else {
            results.errors.push({
              use_case_no: useCaseNo,
              error: `Unknown status: ${status}`
            });
          }

          // Update processing results in real-time
          setProcessingResults({ ...results });

          // Update progress after processing this use case
          setProgress(((i + 1) / useCases.length) * 100);

        } catch (error) {
          if (error.name === 'CanceledError' || error.code === 'ERR_CANCELED') {
            // Request was cancelled, break out of loop
            setCurrentStatus('Processing stopped by user');
            break;
          }
          results.errors.push({
            use_case_no: useCaseNo,
            error: `Request error: ${error.message}`
          });
          setProcessingResults({ ...results });

          // Update progress even for errors
          setProgress(((i + 1) / useCases.length) * 100);
        }
      }

      setResults({
        added: results.added.length,
        results: results.confirmed_duplicates,
        potential: results.potential_duplicates,
        skipped: results.skipped.length,
        errors: results.errors.length,
        details: {
          ...results,
          added_use_cases: results.added
        }
      });

      // Add a small delay to show the final progress step
      setTimeout(() => {
        setCurrentStatus(stopProcessing ? 'Processing stopped' : 'Processing completed successfully!');
        setProgress(100);
      }, 300);

      if (results.potential_duplicates.length > 0) {
        setPendingPotentialDuplicates(results.potential_duplicates);
        setShowPotentialDuplicates(true);
      }

    } catch (error) {
      console.error('Error processing file:', error);
      console.error('Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        timeout: error.code === 'ECONNABORTED'
      });

      let errorMessage = 'Unknown error occurred';
      if (error.name === 'CanceledError' || error.code === 'ERR_CANCELED') {
        errorMessage = 'Processing stopped by user';
        setCurrentStatus(errorMessage);
      } else if (error.code === 'ECONNABORTED') {
        errorMessage = 'Request timed out. The file might be too large or contain too many use cases.';
        setCurrentStatus(`Error: ${errorMessage}`);
      } else if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
        setCurrentStatus(`Error: ${errorMessage}`);
      } else if (error.message) {
        errorMessage = error.message;
        setCurrentStatus(`Error: ${errorMessage}`);
      } else {
        setCurrentStatus(`Error: ${errorMessage}`);
      }
      setResults({
        added: 0,
        results: [],
        potential: [],
        skipped: 0,
        errors: 1,
        details: {
          confirmed_duplicates: [],
          potential_duplicates: [],
          added: [],
          skipped: [],
          errors: [{ error: errorMessage }]
        }
      });
    } finally {
      setProcessing(false);
      setAbortController(null);
    }
  };

  const readFileData = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
          
          if (jsonData.length > 0) {
            const headers = jsonData[0];
            const rows = jsonData.slice(1);
            resolve({ headers, rows });
          } else {
            reject(new Error('No data found in file'));
          }
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsArrayBuffer(file);
    });
  };

  const resetUpload = () => {
    setFile(null);
    setFilePreview(null);
    setResults(null);
    setProgress(0);
    setCurrentStatus('');
    setShowPreview(false);
    setProcessingResults({
      confirmed_duplicates: [],
      potential_duplicates: [],
      added: [],
      skipped: [],
      errors: []
    });
    setCurrentUseCaseIndex(0);
    setTotalUseCases(0);
    setStopProcessing(false);
    setAbortController(null);
    setPendingPotentialDuplicates([]);
    setShowPotentialDuplicates(false);
    setSuccessMessage('');
  };

  return (
    <div>
      <div className="page-header">
        <h1 className="page-title"><FiUpload style={{ display: 'inline', marginRight: '8px' }} /> Upload Excel File</h1>
        <p className="page-description">
          Upload an Excel file with a <strong>Use case</strong> column for batch processing and duplicate detection.
        </p>
      </div>

      <div className="card">
        {!file ? (
          <div
            className={`file-upload-area ${dragOver ? 'dragover' : ''}`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={() => document.getElementById('file-input').click()}
          >
            <div className="file-upload-icon">
              <FiUpload />
            </div>
            <div className="file-upload-text">
              Drag and drop your Excel file here, or click to select
            </div>
            <div className="file-upload-subtext">
              Supports .xlsx and .xls files with "Use case" column
            </div>
            <input
              id="file-input"
              type="file"
              accept=".xlsx,.xls"
              onChange={handleFileSelect}
              style={{ display: 'none' }}
            />
          </div>
        ) : (
          <div>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '24px' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <FiFile style={{ fontSize: '24px', color: '#10b981' }} />
                <div>
                  <div style={{ fontWeight: '500', color: '#1e293b' }}>{file.name}</div>
                  <div style={{ fontSize: '14px', color: '#64748b' }}>
                    {(file.size / 1024 / 1024).toFixed(2)} MB • {filePreview?.totalRows || 0} use cases
                  </div>
                </div>
              </div>
              <button
                onClick={removeFile}
                className="btn-secondary"
                style={{ padding: '8px', borderRadius: '6px' }}
              >
                <FiX />
              </button>
            </div>

            {/* File Preview Section */}
            {filePreview && (
              <div className="mb-6">
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '12px' }}>
                  <h4 style={{ fontSize: '16px', fontWeight: '600', color: '#1e293b' }}>
                    <FiClipboard style={{ display: 'inline', marginRight: '8px' }} /> File Preview
                  </h4>
                  <button
                    onClick={() => setShowPreview(!showPreview)}
                    className="btn-secondary"
                    style={{ padding: '6px 12px', fontSize: '14px' }}
                  >
                    <FiEye style={{ marginRight: '6px' }} />
                    {showPreview ? 'Hide' : 'Show'} Preview
                  </button>
                </div>
                
                {showPreview && (
                  <div style={{ 
                    maxHeight: '400px',
                    overflowY: 'auto',
                    border: '1px solid #e2e8f0',
                    borderRadius: '6px'
                  }}>
                    <div style={{ 
                      display: 'table',
                      width: '100%',
                      borderCollapse: 'collapse'
                    }}>
                      {/* Headers */}
                      <div style={{
                        display: 'table-header-group',
                        backgroundColor: '#f8fafc',
                        position: 'sticky',
                        top: 0,
                        zIndex: 1
                      }}>
                        <div style={{ display: 'table-row' }}>
                          {filePreview.headers.map((header, index) => (
                            <div
                              key={index}
                              style={{
                                display: 'table-cell',
                                padding: '16px',
                                fontWeight: '600',
                                borderBottom: '1px solid #e2e8f0',
                                textAlign: 'left',
                                minWidth: index === 1 ? '600px' : '200px'
                              }}
                            >
                              {header}
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Body */}
                      <div style={{ display: 'table-row-group' }}>
                        {filePreview.rows.map((row, rowIndex) => (
                          <div
                            key={rowIndex}
                            style={{
                              display: 'table-row',
                              backgroundColor: rowIndex % 2 === 0 ? '#ffffff' : '#f8fafc'
                            }}
                          >
                            {row.map((cell, cellIndex) => (
                              <div
                                key={cellIndex}
                                style={{
                                  display: 'table-cell',
                                  padding: '16px',
                                  borderBottom: '1px solid #f1f5f9',
                                  verticalAlign: 'top',
                                  minWidth: cellIndex === 1 ? '600px' : '200px',
                                  fontSize: '14px',
                                  lineHeight: '1.6',
                                  whiteSpace: 'normal',
                                  wordBreak: 'break-word'
                                }}
                              >
                                {cell || ''}
                              </div>
                            ))}
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Footer */}
                    <div style={{ 
                      padding: '12px',
                      fontSize: '14px', 
                      color: '#64748b',
                      textAlign: 'center',
                      borderTop: '1px solid #e2e8f0',
                      backgroundColor: '#f8fafc',
                      position: 'sticky',
                      bottom: 0
                    }}>
                      Showing all {filePreview.totalRows} use cases from the file
                    </div>
                  </div>
                )}
              </div>
            )}

            {!processing && !results && (
              <button
                onClick={processFile}
                className="btn-primary"
                style={{ width: '100%' }}
              >
                <FiPlay style={{ marginRight: '8px' }} />
                Process {filePreview?.totalRows || 0} Use Cases
              </button>
            )}

            {processing && (
              <div className="progress-container">
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
                <div className="progress-text">{currentStatus}</div>
                  <button
                    onClick={() => {
                      setStopProcessing(true);
                      if (abortController) {
                        abortController.abort();
                      }
                    }}
                    className="btn-secondary"
                    style={{ padding: '4px 8px', fontSize: '12px' }}
                  >
                    <FiPause style={{ marginRight: '4px' }} />
                    Stop
                  </button>
                </div>
                <div className="progress-bar">
                  <div className="progress-fill" style={{ width: `${progress}%` }} />
                </div>
                <div style={{
                  marginTop: '4px',
                  fontSize: '12px',
                  color: '#64748b',
                  textAlign: 'center'
                }}>
                  {progress === 100 ? totalUseCases : Math.max(0, currentUseCaseIndex - 1)} of {totalUseCases} use cases processed
                </div>
              </div>
            )}

            {/* Real-time Processing Results */}
            {processing && (
              <div style={{ marginTop: '16px' }}>
                <h4 style={{ fontSize: '14px', fontWeight: '600', marginBottom: '8px', color: '#1e293b' }}>
                  <FiBarChart2 style={{ display: 'inline', marginRight: '8px' }} /> Processing Progress
                </h4>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(5, 1fr)', gap: '8px' }}>
                  <div style={{ 
                    padding: '8px', 
                    backgroundColor: '#dcfce7', 
                    borderRadius: '6px',
                    textAlign: 'center'
                  }}>
                    <div style={{ fontSize: '16px', fontWeight: '700', color: '#16a34a' }}>
                      {processingResults.added.length}
                    </div>
                    <div style={{ fontSize: '10px', color: '#166534' }}>Added</div>
                  </div>
                  <div style={{ 
                    padding: '8px', 
                    backgroundColor: '#fef3c7', 
                    borderRadius: '6px',
                    textAlign: 'center'
                  }}>
                    <div style={{ fontSize: '16px', fontWeight: '700', color: '#d97706' }}>
                      {processingResults.confirmed_duplicates.length}
                    </div>
                    <div style={{ fontSize: '10px', color: '#92400e' }}>Confirmed</div>
                  </div>
                  <div style={{ 
                    padding: '8px', 
                    backgroundColor: '#f3e8ff', 
                    borderRadius: '6px',
                    textAlign: 'center'
                  }}>
                    <div style={{ fontSize: '16px', fontWeight: '700', color: '#9333ea' }}>
                      {processingResults.potential_duplicates.length}
                    </div>
                    <div style={{ fontSize: '10px', color: '#7c3aed' }}>Potential</div>
                  </div>
                  <div style={{ 
                    padding: '8px', 
                    backgroundColor: '#f1f5f9', 
                    borderRadius: '6px',
                    textAlign: 'center'
                  }}>
                    <div style={{ fontSize: '16px', fontWeight: '700', color: '#64748b' }}>
                      {processingResults.skipped.length}
                    </div>
                    <div style={{ fontSize: '10px', color: '#475569' }}>Skipped</div>
                  </div>
                  <div style={{ 
                    padding: '8px', 
                    backgroundColor: '#fee2e2', 
                    borderRadius: '6px',
                    textAlign: 'center'
                  }}>
                    <div style={{ fontSize: '16px', fontWeight: '700', color: '#dc2626' }}>
                      {processingResults.errors.length}
                    </div>
                    <div style={{ fontSize: '10px', color: '#991b1b' }}>Errors</div>
                  </div>
                </div>
              </div>
            )}

            {results && (
              <div style={{ marginTop: '24px' }}>
                <h3 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '16px', color: '#1e293b' }}>
                  Processing Results
                </h3>

                <div className="grid grid-cols-3 gap-4 mb-6">
                  <div className="card" style={{ padding: '16px', textAlign: 'center' }}>
                    <div style={{ fontSize: '24px', fontWeight: '700', color: '#10b981', marginBottom: '4px' }}>
                      {results.added || 0}
                    </div>
                    <div style={{ fontSize: '14px', color: '#64748b' }}>Added</div>
                  </div>
                  <div className="card" style={{ padding: '16px', textAlign: 'center' }}>
                    <div style={{ fontSize: '24px', fontWeight: '700', color: '#f59e0b', marginBottom: '4px' }}>
                      {results.results?.length || 0}
                    </div>
                    <div style={{ fontSize: '14px', color: '#64748b' }}>Confirmed Duplicates</div>
                  </div>
                  <div className="card" style={{ padding: '16px', textAlign: 'center' }}>
                    <div style={{ fontSize: '24px', fontWeight: '700', color: '#8b5cf6', marginBottom: '4px' }}>
                      {results.potential?.length || 0}
                    </div>
                    <div style={{ fontSize: '14px', color: '#64748b' }}>Potential Duplicates</div>
                  </div>
                </div>

                {results.details?.confirmed_duplicates && results.details.confirmed_duplicates.length > 0 && (
                  <div className="mb-6">
                    <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '12px', color: '#dc2626' }}>
                      <FiAlertTriangle style={{ marginRight: '8px' }} />
                      Confirmed Duplicates Found
                    </h4>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                      {results.details.confirmed_duplicates.map((result, index) => (
                        <ConfirmedDuplicateCard key={index} result={result} />
                      ))}
                    </div>
                  </div>
                )}

                {results.details?.added_use_cases && results.details.added_use_cases.length > 0 && (
                  <div className="mb-6">
                    <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '12px', color: '#10b981' }}>
                      <FiCheck style={{ marginRight: '8px' }} />
                      Successfully Added Use Cases
                    </h4>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                      {results.details.added_use_cases.map((useCase, index) => (
                        <AddedUseCaseCard
                          key={index}
                          useCase={useCase}
                        />
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {!processing && showPotentialDuplicates && pendingPotentialDuplicates.length > 0 && (
              <div style={{ marginTop: '24px' }}>
                <h4 style={{ fontSize: '16px', fontWeight: '600', color: '#1e293b', marginBottom: '16px' }}>
                  <FiAlertTriangle style={{ display: 'inline', marginRight: '8px' }} /> Potential Duplicates - Please Review
                </h4>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  {pendingPotentialDuplicates.map((useCase, index) => (
                    <PotentialDuplicateCard
                      key={useCase.unique_id || `${useCase.use_case_no}-${index}`}
                      useCase={useCase}
                      onConfirmDuplicate={handleConfirmDuplicate}
                      onConfirmNew={handleConfirmNew}
                    />
                  ))}
                </div>
                
                {pendingPotentialDuplicates.length > 1 && (
                  <div style={{ marginTop: '16px', display: 'flex', justifyContent: 'center' }}>
                    <button
                      onClick={handleAddAllRemaining}
                      className="btn-primary"
                      disabled={processing}
                    >
                      {processing ? 'Processing...' : 'Add All Remaining as New Use Cases'}
                    </button>
                  </div>
                )}
              </div>
            )}

            {results && (
              <button
                onClick={resetUpload}
                className="btn-primary"
                style={{ marginTop: '24px', width: '100%' }}
              >
                Process Another File
              </button>
            )}
          </div>
        )}
      </div>

      {successMessage && (
        <div style={{
          marginTop: '24px',
          padding: '12px',
          backgroundColor: successMessage.includes('❌') ? '#fee2e2' : '#d1fae5',
          color: successMessage.includes('❌') ? '#991b1b' : '#065f46',
          borderRadius: '6px',
          textAlign: 'center',
          fontWeight: '500',
          border: `2px solid ${successMessage.includes('❌') ? '#fca5a5' : '#86efac'}`
        }}>
          {successMessage}
        </div>
      )}
    </div>
  );
};

export default FileUpload; 