import React, { useEffect } from 'react';
import { useMsal } from '@azure/msal-react';

// Standardized font family
const INTER_FONT = "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif";

const Logout = () => {
  const { instance } = useMsal();

  useEffect(() => {
    // Clear all session data and MSAL cache
    sessionStorage.clear();
    localStorage.clear();

    // Clear MSAL cache
    try {
      // Clear the active account
      instance.setActiveAccount(null);

      // Clear all accounts from cache
      const accounts = instance.getAllAccounts();
      accounts.forEach(account => {
        instance.getTokenCache().removeAccount(account);
      });
    } catch (error) {
      console.log('Error clearing MSAL cache:', error);
    }

    // Immediately perform MSAL logout and redirect
    console.log('Performing immediate MSAL logout...');
    instance.logoutRedirect();
  }, [instance]);

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '100vh',
      backgroundColor: '#f5f5f5',
      padding: '16px',
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      width: '100vw',
      height: '100vh'
    }}>
      <div style={{
        textAlign: 'center',
        maxWidth: '400px'
      }}>
        <h1 style={{
          fontFamily: INTER_FONT,
          fontWeight: 600,
          fontSize: '2rem',
          color: '#2c3e50',
          marginBottom: '16px'
        }}>
          Logging out...
        </h1>
        <p style={{
          fontFamily: INTER_FONT,
          fontSize: '1.1rem',
          lineHeight: 1.6,
          color: '#666',
          margin: 0
        }}>
          Please wait while we redirect you to the sign-in page.
        </p>
      </div>
    </div>
  );
};

export default Logout;