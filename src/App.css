.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-container {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: 280px;
  background: linear-gradient(to bottom, #1e40af, #1e3a8a);
  color: white;
  flex-shrink: 0;
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 1000;
  overflow-y: auto;
  transform: translateX(0);
  transition: transform 0.3s ease;
}

.sidebar.collapsed {
  transform: translateX(-100%);
}

.sidebar-header {
  padding: 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  text-decoration: none;
  color: white;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
}

.sidebar-nav {
  padding: 24px 0;
}

.nav-item {
  display: block;
  padding: 12px 24px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.2s ease;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.nav-item.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-right: 3px solid white;
}

.nav-item-icon {
  display: inline-block;
  width: 20px;
  margin-right: 12px;
  text-align: center;
}

.main-content {
  flex: 1;
  margin-left: 280px;
  min-height: 100vh;
  background: #f8fafc;
  transition: margin-left 0.3s ease;
}

.main-content.sidebar-collapsed {
  margin-left: 0;
}

.header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 16px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-title {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
}

.header-subtitle {
  font-size: 14px;
  color: #64748b;
  margin-top: 4px;
}

.header-left {
  flex: 1;
  display: flex;
  align-items: center;
}

.header-center {
  flex: 2;
  text-align: center;
}

.user-info {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
}

.user-avatar {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

/* Header User Info Styles */
.user-details {
  display: flex;
  flex-direction: column;
  margin-left: 12px;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
}

.user-email {
  font-size: 12px;
  color: #64748b;
  margin-top: 2px;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
}

.page-content {
  padding: 32px;
  max-width: 1100px;
  margin: 0 auto;
}

/* When sidebar is collapsed, use more screen space */
.main-content.sidebar-collapsed .page-content {
  max-width: calc(100vw - 48px);
  padding: 32px 24px 32px 32px;
  margin: 0;
}

.page-header {
  margin-bottom: 32px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 8px;
}

.page-description {
  font-size: 16px;
  color: #64748b;
  line-height: 1.6;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 32px;
}

.dashboard-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
  cursor: pointer;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.dashboard-card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-bottom: 16px;
}

.dashboard-card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
}

.dashboard-card-description {
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
}

.file-upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  padding: 48px 24px;
  text-align: center;
  background: #fafafa;
  transition: all 0.2s ease;
  cursor: pointer;
}

.file-upload-area:hover {
  border-color: #667eea;
  background: #f9fafb;
}

.file-upload-area.dragover {
  border-color: #667eea;
  background: #eff6ff;
}

.file-upload-icon {
  font-size: 48px;
  color: #9ca3af;
  margin-bottom: 16px;
}

.file-upload-text {
  font-size: 16px;
  color: #374151;
  margin-bottom: 8px;
}

.file-upload-subtext {
  font-size: 14px;
  color: #6b7280;
}

.file-preview {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-top: 12px;
}

.preview-table {
  overflow-x: auto;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  background: white;
}

.preview-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
}

.preview-table th {
  background: #f1f5f9;
  font-weight: 600;
  color: #374151;
  padding: 10px 8px;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
  position: sticky;
  top: 0;
  z-index: 10;
}

.preview-table td {
  padding: 8px;
  border-bottom: 1px solid #f1f5f9;
  color: #374151;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.preview-table tr:hover {
  background: #f8fafc;
}

.progress-container {
  margin: 24px 0;
}

.progress-text {
  font-size: 14px;
  color: #374151;
  margin-bottom: 8px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-stats {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 8px;
  margin-top: 16px;
}

.progress-stat {
  padding: 8px;
  border-radius: 6px;
  text-align: center;
  font-size: 12px;
}

.progress-stat-value {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 2px;
}

.table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.table-header {
  padding: 16px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.search-filters {
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.search-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.results-info {
  padding: 16px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f9fafb;
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.table-scroll {
  max-height: 500px;
  overflow-y: auto;
}

.duplicate-warning {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
}

.duplicate-title {
  font-size: 16px;
  font-weight: 600;
  color: #92400e;
  margin-bottom: 8px;
}

.duplicate-content {
  color: #78350f;
  font-size: 14px;
  line-height: 1.5;
}

.duplicate-actions {
  margin-top: 16px;
  display: flex;
  gap: 12px;
}

.attributes-form {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.attributes-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.attributes-full-width {
  grid-column: 1 / -1;
}

/* Potential Duplicates Styles */
.potential-duplicate-card {
  border: 1px solid #e9d5ff;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease-in-out;
}

.potential-duplicate-card:hover {
  box-shadow: 0 2px 4px rgba(124, 58, 237, 0.1);
}

.potential-duplicate-card .card-header {
  transition: background-color 0.2s ease-in-out;
}

.potential-duplicate-card .card-header:hover {
  background-color: #ede9fe;
}

.potential-duplicate-card .card-content {
  border-top: 1px solid #e9d5ff;
}

/* Confirmed Duplicates Styles */
.confirmed-duplicate-card {
  border: 1px solid #fde68a;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease-in-out;
}

.confirmed-duplicate-card:hover {
  box-shadow: 0 2px 4px rgba(217, 119, 6, 0.1);
}

.confirmed-duplicate-card .card-header {
  transition: background-color 0.2s ease-in-out;
}

.confirmed-duplicate-card .card-header:hover {
  background-color: #fef9c3;
}

.confirmed-duplicate-card .card-content {
  border-top: 1px solid #fde68a;
}

/* Button Styles */
.btn-primary {
  background-color: #7c3aed;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
}

.btn-primary:hover {
  background-color: #6d28d9;
}

.btn-secondary {
  background-color: #f8fafc;
  color: #475569;
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.btn-secondary:hover {
  background-color: #f1f5f9;
  border-color: #cbd5e1;
}

/* Login page styles */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8fafc;
}

.login-content {
  max-width: 400px;
  width: 100%;
  padding: 24px;
}

.login-box {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.login-logo {
  width: 120px;
  margin: 0 auto 24px auto;
  display: block;
}

.login-title {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 8px;
  text-align: center;
}

.login-subtitle {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 24px;
  text-align: center;
}

.login-button {
  width: 100%;
  margin-top: 24px;
}

.login-progress {
  text-align: center;
  margin-top: 16px;
  font-size: 14px;
  color: #64748b;
}

/* Alert styles */
.alert {
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.alert-error {
  background: #fef2f2;
  border: 1px solid #fecaca;
}

.alert-content {
  display: flex;
  align-items: flex-start;
}

.alert-icon {
  flex-shrink: 0;
  margin-right: 12px;
  color: #dc2626;
}

.alert-message {
  color: #991b1b;
  font-size: 14px;
  line-height: 1.5;
}

/* User Info Display Styles */
.user-welcome {
  margin: 20px 0;
}

.user-info-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  max-width: 400px;
}

.user-info-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e2e8f0;
}

.user-icon {
  font-size: 24px;
  color: #3b82f6;
  margin-right: 12px;
}

.user-info-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.user-info-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-info-details p {
  margin: 0;
  font-size: 14px;
  color: #64748b;
}

.user-info-details strong {
  color: #1e293b;
  font-weight: 500;
  margin-right: 8px;
}

/* User Menu Styles */
.user-menu-container {
  position: relative;
}

.user-menu-trigger {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.user-menu-trigger:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.user-menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  min-width: 200px;
  z-index: 1000;
}

.user-menu-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 12px 16px;
  background: none;
  border: none;
  cursor: pointer;
  color: #64748b;
  font-size: 14px;
  text-align: left;
  transition: background-color 0.2s;
}

.user-menu-item:hover {
  background-color: #f8fafc;
  color: #1e293b;
}

.menu-item-icon {
  margin-right: 12px;
  font-size: 16px;
}

@media (max-width: 1024px) {
  .sidebar {
    width: 0;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.open {
    width: 280px;
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .header {
    padding: 16px 20px;
  }

  .page-content {
    padding: 20px;
  }

  .search-grid {
    grid-template-columns: 1fr;
  }

  .filter-grid {
    grid-template-columns: 1fr;
  }

  .attributes-grid {
    grid-template-columns: 1fr;
  }

  .mobile-menu-btn {
    display: block !important;
  }

  .mobile-overlay {
    display: block !important;
  }
}

@media (max-width: 640px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .header-title {
    font-size: 20px;
  }

  .page-title {
    font-size: 24px;
  }
} 

/* Logout Page Styles */
.logout-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8fafc;
}

.logout-content {
  text-align: center;
  padding: 32px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  max-width: 400px;
  width: 100%;
}

.logout-content h1 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 16px;
}

.logout-content p {
  color: #64748b;
  font-size: 16px;
  line-height: 1.5;
} 

/* Enhanced User Info Styles */
.user-avatar-img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-menu-header {
  padding: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.user-menu-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-menu-avatar-img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.user-menu-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
}

.user-menu-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-menu-name {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
}

.user-menu-email {
  font-size: 12px;
  color: #64748b;
}

.user-menu-trigger {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 6px 12px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.user-menu-trigger:hover {
  background-color: rgba(0, 0, 0, 0.05);
} 