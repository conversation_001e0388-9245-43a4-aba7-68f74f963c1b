import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>outer as Router } from 'react-router-dom';
import { MsalProvider } from '@azure/msal-react';
import WrapperView from './WrapperView';
import './App.css';

const App = (props) => {
  return (
    <Router>
      <MsalProvider instance={props.Instance}>
        <WrapperView />
      </MsalProvider>
    </Router>
  );
};

export default App;