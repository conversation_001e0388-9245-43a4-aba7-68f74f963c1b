# Docker Deployment Guide

This guide explains how to deploy the AI Use Case Deduplication application using Docker.

## Prerequisites

- Docker and Docker Compose installed
- Environment variables configured
- Access to required external services (Weaviate, Azure OpenAI)

## Quick Start

1. **Copy environment template:**
   ```bash
   cp .env.production .env
   ```

2. **Update environment variables in `.env`:**
   - `WEAVIATE_API_KEY`: Your Weaviate API key
   - `WEAVIATE_URL`: Your Weaviate cluster URL
   - `AZURE_OPENAI_API_KEY`: Your Azure OpenAI API key
   - `AZURE_OPENAI_ENDPOINT`: Your Azure OpenAI endpoint
   - `AZURE_OPENAI_DEPLOYMENT_NAME`: Your GPT deployment name
   - `AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME`: Your embedding deployment name

3. **Deploy the application:**
   ```bash
   ./deploy.sh
   ```

4. **Access the application:**
   - Frontend: http://localhost:5001
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

## Available Scripts

### `./build.sh`
Builds Docker images for both frontend and backend services.

### `./deploy.sh`
Complete deployment script that:
- Stops existing containers
- Builds and starts new containers
- Performs health checks
- Reports deployment status

### `./test-docker.sh`
Tests Docker configuration and image builds without starting services.

## Docker Services

### Backend Service
- **Port:** 8000
- **Health Check:** `/health` endpoint
- **Environment:** Python 3.11 with FastAPI
- **Features:** 
  - Non-root user for security
  - Health monitoring
  - Volume mounting for development

### Frontend Service
- **Port:** 5001
- **Environment:** Node.js with React
- **Features:**
  - Development server with hot reload
  - Environment variable injection
  - Depends on backend service

## Development vs Production

### Development Mode
```bash
docker-compose -f docker-compose.dev.yml up -d
```
- Includes volume mounting for live code changes
- Suitable for development and testing

### Production Mode
```bash
docker-compose up -d
```
- Optimized for production deployment
- Read-only volume mounting
- Better security and performance

## Troubleshooting

### Check service logs:
```bash
docker-compose logs backend
docker-compose logs frontend
```

### Restart services:
```bash
docker-compose restart
```

### Stop all services:
```bash
docker-compose down
```

### Rebuild and restart:
```bash
docker-compose up -d --build
```

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `WEAVIATE_API_KEY` | Weaviate database API key | Yes |
| `WEAVIATE_URL` | Weaviate cluster URL | Yes |
| `AZURE_OPENAI_API_KEY` | Azure OpenAI service key | Yes |
| `AZURE_OPENAI_ENDPOINT` | Azure OpenAI endpoint URL | Yes |
| `AZURE_OPENAI_API_VERSION` | API version (default: 2025-04-01-preview) | Yes |
| `AZURE_OPENAI_DEPLOYMENT_NAME` | GPT model deployment name | Yes |
| `AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME` | Embedding model deployment name | Yes |
| `REACT_APP_BACKEND_URL` | Backend URL for frontend (default: http://localhost:8000) | No |

## Security Notes

- Backend runs as non-root user
- Environment variables are not exposed in images
- Health checks ensure service availability
- Network isolation between services

## Monitoring

The deployment includes health checks for both services:
- Backend: HTTP health check on `/health`
- Frontend: HTTP availability check
- Automatic restart on failure
