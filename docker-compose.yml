services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: usecase-backend
    ports:
      - "8000:8000"
    environment:
      - WEAVIATE_API_KEY=${WEAVIATE_API_KEY}
      - WEAVIATE_URL=${WEAVIATE_URL}
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
      - AZURE_OPENAI_API_VERSION=${AZURE_OPENAI_API_VERSION}
      - AZURE_OPENAI_DEPLOYMENT_NAME=${AZURE_OPENAI_DEPLOYMENT_NAME}
      - AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME=${AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME}
    volumes:
      - ./src:/app/src:ro
    restart: unless-stopped
    networks:
      - usecase-network

  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
      args:
        - REACT_APP_BACKEND_URL=${REACT_APP_BACKEND_URL:-http://localhost:8000}
    container_name: usecase-frontend
    ports:
      - "5001:5001"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - usecase-network

networks:
  usecase-network:
    driver: bridge

volumes:
  backend_data:
