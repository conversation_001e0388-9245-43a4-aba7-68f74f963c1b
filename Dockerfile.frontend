# Frontend Dockerfile
FROM node:latest

# Set working directory
WORKDIR /app

# Accept build arguments
ARG REACT_APP_BACKEND_URL=http://localhost:8000
ENV REACT_APP_BACKEND_URL=$REACT_APP_BACKEND_URL

# Set the port for React dev server
ENV PORT=5001

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy source code
COPY public/ ./public/
COPY src/ ./src/

# Expose port 5001
EXPOSE 5001

# Start the development server
CMD ["npm", "start"]
