{"name": "ai-use-case-deduplication-frontend", "version": "0.1.0", "private": true, "dependencies": {"@azure/msal-browser": "^4.14.0", "@azure/msal-react": "^3.0.14", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^0.27.2", "file-saver": "^2.0.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.4.0", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "styled-components": "^5.3.5", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}